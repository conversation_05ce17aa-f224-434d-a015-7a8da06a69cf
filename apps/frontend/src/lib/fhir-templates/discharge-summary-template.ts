import { v4 as uuidv4 } from 'uuid';

interface DischargeSummaryData {
  // Patient information
  patientId: string;
  patientFirstName: string;
  patientLastName: string;
  patientGender: string;
  patientDateOfBirth: string;
  patientPhone: string;
  patientEmail?: string;
  patientAddress?: string;
  patientCity?: string;
  patientState?: string;
  patientPincode?: string;

  // Doctor information
  doctorId: string;
  doctorName: string;
  doctorQualification?: string;

  // Organization information
  organizationId: string;
  organizationName: string;
  organizationPhone?: string;
  organizationEmail?: string;

  // Encounter information
  encounterId: string;
  encounterStartDate: string;
  encounterEndDate: string;
  encounterStatus: string;

  // Clinical data from DocumentReference JSON fields
  chiefComplaints?: Array<{
    complaint: string;
    recordedDate: string;
    dateRange?: { from: string; to: string };
  }>;
  physicalExaminations?: Array<{
    observation: string;
    result: string;
  }>;
  allergies?: string[];
  medicalHistories?: Array<{
    complaint: string;
    recordedDate: string;
    dateRange?: { from: string; to: string };
  }>;
  familyHistories?: Array<{
    condition: string;
    relation: string;
  }>;
  medications?: Array<{
    medicine: string;
    dosage: string;
    timing: string;
    route: string;
    method: string;
    additionalInstructions?: string;
  }>;
  diagnostics?: Array<{
    testName: string;
    result: string;
    date: string;
  }>;
  procedures?: Array<{
    date: string;
    status: string;
    procedureReason: string;
    outcome: string;
    procedureName: string;
  }>;
  carePlan?: {
    intent: string;
    type: string;
    goal: string;
    description: string;
    notes: string;
  };

  // Document reference for attachment
  documentUrl?: string;
  documentBase64?: string;
  documentTitle?: string;
  documentSize?: number;
}

/**
 * Generate a FHIR Discharge Summary Bundle template based on the example structure
 * This function creates a complete FHIR bundle with all necessary entries
 */
export function generateDischargeSummaryTemplate(data: DischargeSummaryData): any {
  const bundleId = uuidv4();
  const timestamp = new Date().toISOString();
  
  // Generate UUIDs for all resources
  const compositionId = uuidv4();
  const practitionerId = uuidv4();
  const organizationId = uuidv4();
  const patientId = uuidv4();
  const encounterId = uuidv4();
  const appointmentId = uuidv4();
  
  // Generate UUIDs for clinical resources
  const chiefComplaintConditionId = uuidv4();
  const medicalHistoryConditionId = uuidv4();
  const specimenId = uuidv4();
  const diagnosticReportId = uuidv4();
  const procedureId = uuidv4();
  const medicationRequestId = uuidv4();
  const carePlanId = uuidv4();
  const documentReferenceId = uuidv4();

  const bundle = {
    resourceType: "Bundle",
    id: bundleId,
    meta: {
      versionId: "1",
      lastUpdated: timestamp,
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"],
      security: [{
        system: "http://terminology.hl7.org/CodeSystem/v3-Confidentiality",
        code: "V",
        display: "very restricted"
      }]
    },
    identifier: {
      system: "http://hip.in",
      value: bundleId
    },
    type: "document",
    timestamp: timestamp,
    entry: [] as Array<{
      fullUrl: string;
      resource: any;
    }>
  };

  // 1. Composition Entry (Main document structure)
  bundle.entry.push({
    fullUrl: `urn:uuid:${compositionId}`,
    resource: {
      resourceType: "Composition",
      id: compositionId,
      meta: {
        versionId: "1",
        lastUpdated: timestamp,
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DischargeSummaryRecord"]
      },
      language: "en-IN",
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-IN" lang="en-IN"><p><b>Generated Narrative: Composition</b></p><p><b>status</b>: final</p><p><b>type</b>: Discharge Summary</p><p><b>date</b>: ${timestamp}</p><p><b>title</b>: Discharge Summary</p></div>`
      },
      status: "final",
      type: {
        coding: [{
          system: "http://snomed.info/sct",
          code: "373942005",
          display: "Discharge summary"
        }],
        text: "Discharge Summary"
      },
      subject: {
        reference: `urn:uuid:${patientId}`,
        display: "Patient"
      },
      encounter: {
        reference: `urn:uuid:${encounterId}`,
        display: "Encounter"
      },
      date: timestamp,
      author: [{
        reference: `urn:uuid:${practitionerId}`,
        display: "Practitioner"
      }],
      title: "Discharge Summary",
      confidentiality: "N",
      custodian: {
        reference: `urn:uuid:${organizationId}`,
        display: "Organization"
      },
      section: [
        {
          title: "Chief complaints",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "422843007",
              display: "Chief complaint section"
            }]
          },
          entry: [{
            reference: `urn:uuid:${chiefComplaintConditionId}`,
            display: "Condition"
          }]
        },
        {
          title: "Medical History",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "**********",
              display: "Past medical history section"
            }]
          },
          entry: [{
            reference: `urn:uuid:${medicalHistoryConditionId}`,
            display: "Condition"
          }]
        },
        {
          title: "Investigations",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "721981007",
              display: "Diagnostic studies report"
            }]
          },
          entry: [{
            reference: `urn:uuid:${diagnosticReportId}`,
            display: "Diagnostic Report"
          }]
        },
        {
          title: "Procedures",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "**********",
              display: "History of past procedure section"
            }]
          },
          entry: [{
            reference: `urn:uuid:${procedureId}`,
            display: "Procedure"
          }]
        },
        {
          title: "Medications",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "1003606003",
              display: "Medication history section"
            }]
          },
          entry: [{
            reference: `urn:uuid:${medicationRequestId}`,
            display: "MedicationRequest"
          }]
        },
        {
          title: "Care Plan",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "734163000",
              display: "Care plan"
            }]
          },
          entry: [{
            reference: `urn:uuid:${carePlanId}`,
            display: "Care plan"
          }]
        },
        {
          title: "Document Reference",
          code: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "373942005",
              display: "Discharge summary"
            }]
          },
          entry: [{
            reference: `urn:uuid:${documentReferenceId}`,
            display: "Discharge Summary"
          }]
        }
      ]
    }
  });

  // 2. Practitioner Entry
  bundle.entry.push({
    fullUrl: `urn:uuid:${practitionerId}`,
    resource: {
      resourceType: "Practitioner",
      id: practitionerId,
      meta: {
        versionId: "1",
        lastUpdated: timestamp,
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Practitioner</b></p><p><b>name</b>: ${data.doctorName}</p></div>`
      },
      identifier: [{
        type: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/v2-0203",
            code: "MD",
            display: "Medical License number"
          }]
        },
        system: "https://doctor.ndhm.gov.in",
        value: data.doctorId
      }],
      name: [{
        text: data.doctorName
      }]
    }
  });

  // 3. Organization Entry
  bundle.entry.push({
    fullUrl: `urn:uuid:${organizationId}`,
    resource: {
      resourceType: "Organization",
      id: organizationId,
      meta: {
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Organization</b></p><p><b>name</b>: ${data.organizationName}</p></div>`
      },
      identifier: [{
        type: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/v2-0203",
            code: "PRN",
            display: "Provider number"
          }]
        },
        system: "https://facility.ndhm.gov.in",
        value: data.organizationId
      }],
      name: data.organizationName,
      telecom: [
        ...(data.organizationPhone ? [{
          system: "phone",
          value: data.organizationPhone,
          use: "work"
        }] : []),
        ...(data.organizationEmail ? [{
          system: "email",
          value: data.organizationEmail,
          use: "work"
        }] : [])
      ]
    }
  });

  // 4. Patient Entry
  bundle.entry.push({
    fullUrl: `urn:uuid:${patientId}`,
    resource: {
      resourceType: "Patient",
      id: patientId,
      meta: {
        versionId: "1",
        lastUpdated: timestamp,
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Patient</b></p><p><b>name</b>: ${data.patientFirstName} ${data.patientLastName}</p><p><b>gender</b>: ${data.patientGender}</p><p><b>birthDate</b>: ${data.patientDateOfBirth}</p></div>`
      },
      identifier: [{
        type: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/v2-0203",
            code: "MR",
            display: "Medical record number"
          }]
        },
        system: "https://healthid.ndhm.gov.in",
        value: data.patientId
      }],
      name: [{
        text: `${data.patientFirstName} ${data.patientLastName}`
      }],
      telecom: [{
        system: "phone",
        value: data.patientPhone,
        use: "home"
      }],
      gender: data.patientGender.toLowerCase(),
      birthDate: data.patientDateOfBirth
    }
  });

  // 5. Encounter Entry
  bundle.entry.push({
    fullUrl: `urn:uuid:${encounterId}`,
    resource: {
      resourceType: "Encounter",
      id: encounterId,
      meta: {
        lastUpdated: timestamp,
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Encounter</b></p><p><b>status</b>: ${data.encounterStatus}</p><p><b>class</b>: inpatient encounter</p></div>`
      },
      identifier: [{
        system: "https://ndhm.in",
        value: data.encounterId
      }],
      status: data.encounterStatus,
      class: {
        system: "http://terminology.hl7.org/CodeSystem/v3-ActCode",
        code: "IMP",
        display: "inpatient encounter"
      },
      subject: {
        reference: `urn:uuid:${patientId}`,
        display: "Patient"
      },
      period: {
        start: data.encounterStartDate,
        end: data.encounterEndDate
      },
      hospitalization: {
        dischargeDisposition: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/discharge-disposition",
            code: "home",
            display: "Home"
          }],
          text: "Discharged to Home Care"
        }
      }
    }
  });

  // 6. Chief Complaint Condition Entry
  if (data.chiefComplaints && data.chiefComplaints.length > 0) {
    const chiefComplaint = data.chiefComplaints[0];
    bundle.entry.push({
      fullUrl: `urn:uuid:${chiefComplaintConditionId}`,
      resource: {
        resourceType: "Condition",
        id: chiefComplaintConditionId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Condition</b></p><p><b>clinicalStatus</b>: Active</p><p><b>code</b>: ${chiefComplaint.complaint}</p></div>`
        },
        clinicalStatus: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
            code: "active",
            display: "Active"
          }]
        },
        code: {
          coding: [{
            system: "http://snomed.info/sct",
            code: "22298006",
            display: "Chief complaint"
          }],
          text: chiefComplaint.complaint
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        }
      }
    });
  }

  // 7. Medical History Condition Entry
  if (data.medicalHistories && data.medicalHistories.length > 0) {
    const medicalHistory = data.medicalHistories[0];
    bundle.entry.push({
      fullUrl: `urn:uuid:${medicalHistoryConditionId}`,
      resource: {
        resourceType: "Condition",
        id: medicalHistoryConditionId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Condition</b></p><p><b>clinicalStatus</b>: Recurrence</p><p><b>code</b>: ${medicalHistory.complaint}</p></div>`
        },
        clinicalStatus: {
          coding: [{
            system: "http://terminology.hl7.org/CodeSystem/condition-clinical",
            code: "recurrence",
            display: "Recurrence"
          }]
        },
        code: {
          coding: [{
            system: "http://snomed.info/sct",
            code: "440700005",
            display: "History of medical condition"
          }],
          text: medicalHistory.complaint
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        }
      }
    });
  }

  // 8. Specimen Entry (for diagnostics)
  if (data.diagnostics && data.diagnostics.length > 0) {
    bundle.entry.push({
      fullUrl: `urn:uuid:${specimenId}`,
      resource: {
        resourceType: "Specimen",
        id: specimenId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Specimen"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Specimen</b></p><p><b>type</b>: Serum specimen</p></div>`
        },
        type: {
          coding: [{
            system: "http://snomed.info/sct",
            code: "119364003",
            display: "Serum specimen"
          }]
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        },
        receivedTime: timestamp,
        collection: {
          collectedDateTime: timestamp
        }
      }
    });
  }

  // 9. Diagnostic Report Entry
  if (data.diagnostics && data.diagnostics.length > 0) {
    const diagnostic = data.diagnostics[0];
    bundle.entry.push({
      fullUrl: `urn:uuid:${diagnosticReportId}`,
      resource: {
        resourceType: "DiagnosticReport",
        id: diagnosticReportId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DiagnosticReportLab"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: DiagnosticReport</b></p><p><b>status</b>: final</p><p><b>code</b>: ${diagnostic.testName}</p></div>`
        },
        identifier: [{
          system: "https://lab.example.com/reports",
          value: `report-${diagnosticReportId}`
        }],
        status: "final",
        category: [{
          coding: [{
            system: "http://snomed.info/sct",
            code: "708196005",
            display: "Laboratory service"
          }]
        }],
        code: {
          coding: [{
            system: "http://loinc.org",
            code: "24331-1",
            display: "Laboratory panel"
          }],
          text: diagnostic.testName
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        },
        issued: diagnostic.date,
        performer: [{
          reference: `urn:uuid:${organizationId}`,
          display: data.organizationName
        }],
        resultsInterpreter: [{
          reference: `urn:uuid:${practitionerId}`,
          display: data.doctorName
        }],
        specimen: [{
          reference: `urn:uuid:${specimenId}`,
          display: "Specimen"
        }],
        conclusion: diagnostic.result
      }
    });
  }

  // 10. Procedure Entry
  if (data.procedures && data.procedures.length > 0) {
    const procedure = data.procedures[0];
    bundle.entry.push({
      fullUrl: `urn:uuid:${procedureId}`,
      resource: {
        resourceType: "Procedure",
        id: procedureId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Procedure</b></p><p><b>status</b>: ${procedure.status.toLowerCase()}</p><p><b>code</b>: ${procedure.procedureName}</p></div>`
        },
        status: procedure.status.toLowerCase(),
        code: {
          coding: [{
            system: "http://snomed.info/sct",
            code: "387713003",
            display: "Surgical procedure"
          }],
          text: procedure.procedureName
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        },
        performedDateTime: procedure.date,
        reasonCode: [{
          text: procedure.procedureReason
        }],
        outcome: {
          text: procedure.outcome
        }
      }
    });
  }

  // 11. Medication Request Entry
  if (data.medications && data.medications.length > 0) {
    const medication = data.medications[0];
    bundle.entry.push({
      fullUrl: `urn:uuid:${medicationRequestId}`,
      resource: {
        resourceType: "MedicationRequest",
        id: medicationRequestId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: MedicationRequest</b></p><p><b>status</b>: active</p><p><b>medication</b>: ${medication.medicine}</p></div>`
        },
        status: "active",
        intent: "order",
        medicationCodeableConcept: {
          coding: [{
            system: "http://snomed.info/sct",
            code: "387207008",
            display: "Medication"
          }],
          text: medication.medicine
        },
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        },
        authoredOn: timestamp,
        requester: {
          reference: `urn:uuid:${practitionerId}`,
          display: data.doctorName
        },
        dosageInstruction: [{
          text: `${medication.dosage} ${medication.timing} via ${medication.route}`,
          route: {
            coding: [{
              system: "http://snomed.info/sct",
              code: "26643006",
              display: medication.route
            }]
          },
          method: {
            text: medication.method
          },
          additionalInstruction: medication.additionalInstructions ? [{
            text: medication.additionalInstructions
          }] : undefined
        }]
      }
    });
  }

  // 12. Care Plan Entry
  if (data.carePlan) {
    bundle.entry.push({
      fullUrl: `urn:uuid:${carePlanId}`,
      resource: {
        resourceType: "CarePlan",
        id: carePlanId,
        meta: {
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/CarePlan"]
        },
        text: {
          status: "generated",
          div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: CarePlan</b></p><p><b>status</b>: active</p><p><b>intent</b>: ${data.carePlan.intent}</p></div>`
        },
        status: "active",
        intent: data.carePlan.intent,
        category: [{
          coding: [{
            system: "http://snomed.info/sct",
            code: "734163000",
            display: "Care plan"
          }],
          text: data.carePlan.type
        }],
        subject: {
          reference: `urn:uuid:${patientId}`,
          display: "Patient"
        },
        created: timestamp,
        author: {
          reference: `urn:uuid:${practitionerId}`,
          display: data.doctorName
        },
        goal: [{
          description: {
            text: data.carePlan.goal
          }
        }],
        description: data.carePlan.description,
        note: [{
          text: data.carePlan.notes
        }]
      }
    });
  }

  // 13. Document Reference Entry (for attachment)
  bundle.entry.push({
    fullUrl: `urn:uuid:${documentReferenceId}`,
    resource: {
      resourceType: "DocumentReference",
      id: documentReferenceId,
      meta: {
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: DocumentReference</b></p><p><b>status</b>: current</p><p><b>type</b>: Discharge summary</p></div>`
      },
      status: "current",
      docStatus: "final",
      type: {
        coding: [{
          system: "http://loinc.org",
          code: "18842-5",
          display: "Discharge summary"
        }]
      },
      category: [{
        coding: [{
          system: "http://snomed.info/sct",
          code: "373942005",
          display: "Discharge summary"
        }]
      }],
      subject: {
        reference: `urn:uuid:${patientId}`,
        display: "Patient"
      },
      date: timestamp,
      author: [{
        reference: `urn:uuid:${practitionerId}`,
        display: "Practitioner"
      }],
      custodian: {
        reference: `urn:uuid:${organizationId}`,
        display: "Organization"
      },
      description: data.documentTitle || "Discharge Summary Document",
      content: [{
        attachment: {
          contentType: "application/pdf",
          data: data.documentBase64 || "",
          title: data.documentTitle || "Discharge Summary Document",
          size: data.documentSize || 0
        }
      }],
      context: {
        encounter: [{
          reference: `urn:uuid:${encounterId}`,
          display: "Encounter"
        }]
      }
    }
  });

  // 14. Appointment Entry (for follow-up)
  bundle.entry.push({
    fullUrl: `urn:uuid:${appointmentId}`,
    resource: {
      resourceType: "Appointment",
      id: appointmentId,
      meta: {
        profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]
      },
      text: {
        status: "generated",
        div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: Appointment</b></p><p><b>status</b>: booked</p><p><b>serviceType</b>: Follow-up consultation</p></div>`
      },
      status: "booked",
      serviceCategory: [{
        coding: [{
          system: "http://snomed.info/sct",
          code: "408443003",
          display: "General medical practice"
        }]
      }],
      serviceType: [{
        coding: [{
          system: "http://snomed.info/sct",
          code: "11429006",
          display: "Consultation"
        }]
      }],
      appointmentType: {
        coding: [{
          system: "http://snomed.info/sct",
          code: "185389009",
          display: "Follow-up visit"
        }]
      },
      description: "Follow-up consultation for discharge summary",
      start: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(), // 30 minutes later
      created: timestamp,
      participant: [
        {
          actor: {
            reference: `urn:uuid:${patientId}`,
            display: "Patient"
          },
          status: "accepted"
        },
        {
          actor: {
            reference: `urn:uuid:${practitionerId}`,
            display: "Practitioner"
          },
          status: "accepted"
        }
      ]
    }
  });

  // Add signature to the bundle (as per example structure)
  (bundle as any).signature = {
    type: [{
      system: "urn:iso-astm:E1762-95:2013",
      code: "1.2.840.10065.********",
      display: "Author's Signature"
    }],
    when: timestamp,
    who: {
      reference: `urn:uuid:${practitionerId}`,
      display: "Practitioner"
    },
    sigFormat: "image/jpeg",
    data: "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAA4AW8DASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KK/GT/guT/wcPfHr/gnd/wUM074LfCTwF8PPFUWoaJp11bpq+lajqOpX97dSyosUKW11DnO2NVQIzFieTkAeEeI/wDg6E/bu/Zcu9K8SfHH9k3S/DvgF70Wdy954N8Q+F3vZGjdkghvryaaGOU7C2DDISqPhe4ilUjOPOtFdq77p2f4l1KcoS5d3ZP5NX/I/oSor5W/4JP/APBXL4af8Fcfgdc+KPBAvdG17QWit/EnhrUGU3mizyKSpDL8ssDlJPLlAG8I25UYMi/VNbVKcqcuWe/+ZjCpGavEKKKKgsKKK/nl8Yf8HTf7Z3jb9rj4g/Df4RfA34eePZPCut6la2mn6X4S1zWtUFlbXTQCWZbW9GcDZucRqu5hwMgVHtF7RUurTfyVr/mXyPkdToml997fkf0NUV+AHgj/AIO4/wBoP9ln4v22g/tYfs0DwzZarFBcwwabpOpeGNYtLQyukl0ltqUkou1Oxgih4F3RsDJ/d/bL4KftYeD/ANp39lvT/i18OtVg8ReFdb0mXU9OuACm/wAsOGikU/MkiSIyOp5VlYHpV1GoUJYh/DHfy3/yZELyrKh9p7ed/wDh/wCkem0V/N78Ef8Ag61/bx/ad1HUbb4afs+fDv4gz6Uiy3sPhrwN4h1eSyRyQjSi3v2KAkEAsACQcV+g/wDwRW/4Kbftn/toftL6/wCGv2iv2eP+FU+DLDw5LqNlrH/CE63oHnXy3FvGlv5l/PJHJujklbaoDfu85xmtKdKU3Zddm/uTf6feROpGGvml97t+p+ndFfhX/wAFLf8Ag5S/ah/Zs/4KkeO/2f8A4P8Awu+HPjgaDfW1jotm/h7VtV1vUmexgOXAS1vE8xgXcgJFwq85wTXon/BOH/gsR/wUG/aM/bX8BeC/i5+yt/whHw5167lh1vXf+Fc+I9I/s2JbeV1f7Td3DwR/vFQfOp3bto5INZ4X/aIwlT2ltf8Ar/hi8R+5clPeO/yP2QooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAP/9k="
  };

  return bundle;
}

export type { DischargeSummaryData };
