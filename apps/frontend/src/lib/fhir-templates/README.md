# FHIR Templates

This directory contains FHIR bundle templates that generate structured FHIR documents based on the example bundles and database schema.

## Discharge Summary Template

The `discharge-summary-template.ts` file contains a complete FHIR Discharge Summary bundle template that:

1. **Follows the exact structure** of the example bundle from `/packages/fhir-observation/examples/Bundle-DischargeSummary-example-04.json`
2. **Maps database fields** from the `DocumentReference` table's JSON fields to FHIR resources
3. **Generates all required entries** including:
   - Composition (main document structure)
   - Practitioner (doctor information)
   - Organization (hospital/clinic information)
   - Patient (patient demographics)
   - Encounter (admission/discharge details)
   - Condition entries (chief complaints, medical history)
   - Specimen (for diagnostic tests)
   - DiagnosticReport (test results)
   - Procedure (procedures performed)
   - MedicationRequest (medications prescribed)
   - CarePlan (discharge care plan)
   - DocumentReference (for attachments)
   - Appointment (follow-up appointments)

## Usage

### API Route
The template is used by the `/api/fhir/discharge-summary-template` route which:
- Takes a discharge summary ID and related consultation/patient/organization IDs
- Fetches the discharge summary data from the database
- Maps the JSON fields to the template structure
- Generates a complete FHIR bundle
- Saves it to the `FhirBundle` table with `bundleType: "DischargeSummary"`

### Automatic Generation
The template is automatically called when a discharge summary form is saved via the `/api/discharge-summary` route.

### Testing
Use the `/api/fhir/test-discharge-template` route to test the template with sample data.

## Database Mapping

The template maps the following JSON fields from the `DocumentReference` table:

- `chiefComplaintsJson` → Condition resources for chief complaints
- `physicalExaminationsJson` → Used in diagnostic reports
- `allergiesJson` → Patient allergies (could be expanded to AllergyIntolerance resources)
- `medicalHistoriesJson` → Condition resources for medical history
- `familyHistoriesJson` → Could be expanded to FamilyMemberHistory resources
- `medicationsJson` → MedicationRequest resources
- `diagnosticsJson` → DiagnosticReport and Specimen resources
- `proceduresJson` → Procedure resources
- `carePlanJson` → CarePlan resource

## FHIR Compliance

The generated bundles are compliant with:
- FHIR R4 specification
- ABDM (Ayushman Bharat Digital Mission) profiles
- NDHM (National Digital Health Mission) structure definitions

All resources include proper:
- Meta profiles pointing to NDHM structure definitions
- SNOMED CT and LOINC coding systems
- Proper resource references using UUIDs
- Generated narrative text for each resource

## Future Enhancements

1. **Document Attachments**: Add support for base64 encoded PDF attachments in the DocumentReference
2. **Additional Resources**: Expand to include AllergyIntolerance, FamilyMemberHistory, etc.
3. **Validation**: Add FHIR validation against the official schemas
4. **Customization**: Allow template customization based on organization preferences
