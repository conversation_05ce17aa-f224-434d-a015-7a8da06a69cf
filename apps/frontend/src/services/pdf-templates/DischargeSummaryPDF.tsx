import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";

// Helper function to format dates in IST timezone
const formatDateInIST = (date: Date | string, formatString: string = "PPP p") => {
  const dateObj = new Date(date);
  // Convert to IST (UTC+5:30)
  const istDate = new Date(dateObj.getTime() + (5.5 * 60 * 60 * 1000));
  return format(istDate, formatString) + " IST";
};
import { BasePDF } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  section: { marginBottom: 20 },
  heading: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    paddingBottom: 4,
  },
  subHeading: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 6,
    marginTop: 10,
  },
  detailSection: {
    marginBottom: 15,
    padding: 10,
    borderWidth: 1,
    borderColor: "#ddd",
    borderStyle: "solid",
  },
  detailRow: {
    flexDirection: "row",
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 10,
    fontWeight: "bold",
    width: "30%",
  },
  detailValue: {
    fontSize: 10,
    width: "70%",
  },
  notes: {
    fontSize: 10,
    marginTop: 10,
    lineHeight: 1.5,
  },
  textBlock: {
    fontSize: 10,
    lineHeight: 1.4,
    marginBottom: 8,
  },
  patientHeader: {
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 10,
    backgroundColor: "#f5f5f5",
    padding: 8,
  },
});

interface DischargeSummaryPDFProps {
  consultationData: ConsultationData & {
    dischargeSummaries?: any[];
  };
  options?: PdfGenerationOptions;
}

export const DischargeSummaryPDF: React.FC<DischargeSummaryPDFProps> = ({
  consultationData,
  options,
}) => {
  console.log("DischargeSummaryPDF consultationData:", consultationData.DocumentReference);
  const { DocumentReference = [] } = consultationData;
  const  dischargeSummaries  = DocumentReference;


  const formatDate = (date: string | Date) => {
    return formatDateInIST(date, "PPP");
  };

  const formatDateTime = (date: string | Date) => {
    return formatDateInIST(date, "PPP p");
  };

  return (
    <BasePDF
      consultationData={consultationData}
      title="Discharge Summary"
      options={options}
    >
      <View style={styles.section}>
        <Text style={styles.notes}>
          Total Records: {dischargeSummaries.length}
        </Text>
        <Text style={styles.notes}>
          Generated on: {formatDateInIST(new Date(), "PPP")}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.heading}>Discharge Summary Records</Text>
        {dischargeSummaries.length > 0 ? (
          dischargeSummaries.map((summary: any, idx: number) => (
            <View key={idx} style={styles.detailSection}>
              {/* Patient Header */}
              <Text style={styles.patientHeader}>
                Record #{idx + 1} -{" "}
                {summary.patient
                  ? `${summary.patient.firstName} ${summary.patient.lastName}`
                  : summary.consultation?.patient
                    ? `${summary.consultation.patient.firstName} ${summary.consultation.patient.lastName}`
                    : "Unknown Patient"}
              </Text>

              {/* Basic Information */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Doctor:</Text>
                <Text style={styles.detailValue}>
                  {summary.doctor?.user?.name ||
                    summary.consultation?.doctor?.user?.name ||
                    summary.author ||
                    "Unknown Doctor"}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status:</Text>
                <Text style={styles.detailValue}>
                  {summary.docStatus || "Draft"}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>
                  {summary.typeDisplay || summary.type || "Discharge Summary"}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Category:</Text>
                <Text style={styles.detailValue}>
                  {summary.categoryDisplay || summary.category || "Clinical"}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Created:</Text>
                <Text style={styles.detailValue}>
                  {formatDateTime(summary.createdAt)}
                </Text>
              </View>

              {/* Admission & Discharge Dates */}
              {summary.content.admissionDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Admission Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(summary.content.admissionDate)}
                  </Text>
                </View>
              )}

              {summary.content.dischargeDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Discharge Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(summary.content.dischargeDate)}
                  </Text>
                </View>
              )}

              {/* Clinical Information */}
              {summary.content.chiefComplaint && (
                <View>
                  <Text style={styles.subHeading}>Chief Complaint:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.chiefComplaint}
                  </Text>
                </View>
              )}

              {summary.content.diagnosisName && (
                <View>
                  <Text style={styles.subHeading}>Primary Diagnosis:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.diagnosisName}
                    {summary.content.diagnosisCode &&
                      ` (${summary.content.diagnosisCode})`}
                  </Text>
                </View>
              )}

              {summary.content.medicalHistory && (
                <View>
                  <Text style={styles.subHeading}>Medical History:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.medicalHistory}
                  </Text>
                </View>
              )}

              {summary.content.investigations && (
                <View>
                  <Text style={styles.subHeading}>Investigations:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.investigations}
                  </Text>
                </View>
              )}

              {summary.content.vitalSigns && (
                <View>
                  <Text style={styles.subHeading}>Vital Signs:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.vitalSigns}
                  </Text>
                </View>
              )}

              {summary.content.labResults && (
                <View>
                  <Text style={styles.subHeading}>Lab Results:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.labResults}
                  </Text>
                </View>
              )}

              {summary.content.treatmentProvided && (
                <View>
                  <Text style={styles.subHeading}>Treatment Provided:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.treatmentProvided}
                  </Text>
                </View>
              )}

              {summary.content.medications && (
                <View>
                  <Text style={styles.subHeading}>Medications:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.medications}
                  </Text>
                </View>
              )}

              {summary.content.dischargeSummary && (
                <View>
                  <Text style={styles.subHeading}>Discharge Summary:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.dischargeSummary}
                  </Text>
                </View>
              )}

              {summary.content.dischargeInstructions && (
                <View>
                  <Text style={styles.subHeading}>Discharge Instructions:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.dischargeInstructions}
                  </Text>
                </View>
              )}

              {summary.content.carePlan && (
                <View>
                  <Text style={styles.subHeading}>Care Plan:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.carePlan}
                  </Text>
                </View>
              )}

              {summary.content.recommendations && (
                <View>
                  <Text style={styles.subHeading}>Recommendations:</Text>
                  <Text style={styles.textBlock}>
                    {summary.content.recommendations}
                  </Text>
                </View>
              )}

              {summary.content.followUpDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Follow-up Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(summary.content.followUpDate)}
                  </Text>
                </View>
              )}

              {summary.content.condition && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Condition:</Text>
                  <Text style={styles.detailValue}>
                    {summary.content.condition}
                  </Text>
                </View>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.notes}>No discharge summaries found.</Text>
        )}
      </View>
    </BasePDF>
  );
};
