"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";

// Define the form schema - keeping it simple for now
const formSchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().min(1, "Consultation selection is required"),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),

  // Chief Complaints
  chiefComplaint: z.string().optional(),

  // Medical History
  medicalHistory: z.string().optional(),

  // Investigations
  investigations: z.string().optional(),
  vitalSigns: z.string().optional(),
  labResults: z.string().optional(),

  // Procedures
  procedureName: z.string().optional(),
  procedureCode: z.string().optional(),
  procedureDate: z.string().optional(),
  proceduresPerformed: z.string().optional(),

  // Medications
  medicationName: z.string().optional(),
  medicationCode: z.string().optional(),
  medicationInstructions: z.string().optional(),
  medicationsList: z.string().optional(),

  // Care Plan
  carePlan: z.string().optional(),
  followUpDate: z.string().optional(),
  dischargeInstructions: z.string().optional(),
  recommendations: z.string().optional(),

  // Legacy fields
  diagnosisName: z.string().optional(),
  diagnosisCode: z.string().optional(),
  dischargeSummary: z.string().optional(),
  condition: z.string().optional(),
  treatmentProvided: z.string().optional(),

  // New structured fields (as JSON strings for now)
  allergies: z.string().optional(),
  physicalExaminations: z.string().optional(),

  // Care Plan structured fields
  carePlanIntent: z.string().optional(),
  carePlanType: z.string().optional(),
  carePlanGoal: z.string().optional(),
  carePlanDescription: z.string().optional(),
  carePlanNotes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface DischargeSummaryFormProps {
  patientId: string;
  doctorId: string;
  consultationId: string; // Now required
  onCancel?: () => void;
  onSuccess?: () => void;
  onRefresh?: () => void;
  initialData?: Partial<FormValues>;
  mode?: "create" | "edit";
  dischargeSummaryId?: string;
}

export function DischargeSummaryForm({
  patientId,
  doctorId,
  consultationId,
  onCancel,
  onSuccess,
  onRefresh,
  initialData,
  mode = "create",
  dischargeSummaryId,
}: DischargeSummaryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      patientId,
      doctorId,
      consultationId,
      admissionDate: initialData?.admissionDate || "",
      dischargeDate: initialData?.dischargeDate || "",

      // Chief Complaints
      chiefComplaint: initialData?.chiefComplaint || "",

      // Medical History

      // Investigations
      investigations: initialData?.investigations || "",
      vitalSigns: initialData?.vitalSigns || "",
      labResults: initialData?.labResults || "",

      // Procedures
      procedureName: initialData?.procedureName || "",
      procedureCode: initialData?.procedureCode || "",
      procedureDate: initialData?.procedureDate || "",
      proceduresPerformed: initialData?.proceduresPerformed || "",

      // Medications
      medicationName: initialData?.medicationName || "",
      medicationCode: initialData?.medicationCode || "",
      medicationInstructions: initialData?.medicationInstructions || "",
      medicationsList: initialData?.medicationsList || "",

      // Care Plan
      carePlan: initialData?.carePlan || "",
      followUpDate: initialData?.followUpDate || "",
      dischargeInstructions: initialData?.dischargeInstructions || "",
      recommendations: initialData?.recommendations || "",

      // Legacy fields
      diagnosisName: initialData?.diagnosisName || "",
      diagnosisCode: initialData?.diagnosisCode || "",
      dischargeSummary: initialData?.dischargeSummary || "",
      condition: initialData?.condition || "",
      treatmentProvided: initialData?.treatmentProvided || "",

      // New structured fields
      physicalExaminations: "",

      // Care Plan structured fields
      carePlanIntent: "plan",
      carePlanType: "",
      carePlanGoal: "",
      carePlanDescription: "",
      carePlanNotes: "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // Convert simple text fields to structured JSON format
      const payload = {
        ...data,
        // Convert text fields to structured JSON for FHIR compliance
        chiefComplaintsJson: data.chiefComplaint ? JSON.stringify([{
          complaint: data.chiefComplaint,
          recordedDate: new Date().toISOString().split('T')[0],
          dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            to: new Date().toISOString().split('T')[0],
          },
        }]) : null,

        physicalExaminationsJson: data.physicalExaminations ? JSON.stringify([{
          observation: "Physical Examination",
          result: data.physicalExaminations,
        }]) : null,

        medicationsJson: data.medicationsList ? JSON.stringify([{
          medicine: data.medicationsList,
          dosage: "1-0-1",
          timing: "1-1-D", // Fixed format: 1 time every 1 day
          route: "Oral",
          method: "swallow",
          additionalInstructions: data.medicationInstructions || "",
        }]) : null,

        proceduresJson: data.proceduresPerformed ? JSON.stringify([{
          date: data.procedureDate || new Date().toISOString().split('T')[0],
          status: "COMPLETED",
          procedureReason: "Medical procedure",
          outcome: "Successful",
          procedureName: data.proceduresPerformed,
        }]) : null,

        // Care Plan JSON - required by external API
        carePlanJson: JSON.stringify({
          intent: data.carePlanIntent || "plan",
          type: data.carePlanType || "Regular check up",
          goal: data.carePlanGoal || "Patient care",
          description: data.carePlanDescription || data.carePlan || "Standard care plan",
          notes: data.carePlanNotes || data.dischargeInstructions || "Follow standard care protocols",
        }),
      };

      let response;
      if (mode === "edit" && dischargeSummaryId) {
        response = await Fetch.put(
          `/api/discharge-summary/${dischargeSummaryId}`,
          payload,
        );
      } else {
        response = await Fetch.post("/api/discharge-summary", payload);
      }

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success(
        mode === "edit"
          ? "Discharge summary updated successfully"
          : "Discharge summary created successfully",
      );
      form.reset();

      // Call refresh callback if provided
      if (onRefresh) {
        onRefresh();
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error saving discharge summary:", error);
      toast.error("Failed to save discharge summary");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">


        {/* Chief Complaints Section */}
        <Card>
          <CardHeader>
            <CardTitle>Chief Complaints</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="chiefComplaint"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Chief Complaints</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Patient's primary complaints and symptoms"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Medical History & Allergies Section */}


        {/* Investigations Section */}
        <Card>
          <CardHeader>
            <CardTitle>Investigations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="investigations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Investigations Performed</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="List of investigations, tests, and diagnostic procedures performed"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="physicalExaminations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Physical Examinations</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Physical examination findings and observations"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Procedures Section */}
        <Card>
          <CardHeader>
            <CardTitle>Procedures</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="proceduresPerformed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Procedures Performed</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed description of all procedures performed during admission"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


          </CardContent>
        </Card>

        {/* Medications Section */}
        <Card>
          <CardHeader>
            <CardTitle>Medications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="medicationsList"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Medications List</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Complete list of medications prescribed at discharge with dosages and instructions"
                      rows={5}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


          </CardContent>
        </Card>
        {/* Care Plan Section */}


        {/* Document Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle>Document Upload (Optional)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Upload a PDF discharge summary document that will be included in
              the FHIR bundle when discharge summary is saved.
            </p>
            <AzureFhirUpload
              consultationId={consultationId}
              patientId={patientId}
              bundleType="DischargeSummary"
              title="Upload Discharge Summary Document"
              description="Upload a PDF discharge summary document"
              onUploadSuccess={(_result) => {
                toast.success(
                  "Discharge summary document uploaded successfully. It will be included when discharge summary is saved.",
                );
              }}
              hideWhenFilesExist={false}
            />
            <UploadedFilesList
              consultationId={consultationId}
              bundleType="DischargeSummary"
              title="Uploaded Discharge Summary Documents"
              description="Documents that will be included in the FHIR bundle"
              showOnlyGenerated={false}
              className="mt-4"
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? mode === "edit"
                ? "Updating..."
                : "Creating..."
              : mode === "edit"
                ? "Update Discharge Summary"
                : "Create Discharge Summary"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
