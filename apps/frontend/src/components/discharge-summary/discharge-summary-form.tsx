"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";

// Define the form schema with structured fields matching the FHIR template
const formSchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().min(1, "Consultation selection is required"),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),

  // Chief Complaints - structured
  chiefComplaint: z.string().optional(),
  chiefComplaintDateFrom: z.string().optional(),
  chiefComplaintDateTo: z.string().optional(),

  // Medical History - structured
  medicalHistory: z.string().optional(),
  medicalHistoryDateFrom: z.string().optional(),
  medicalHistoryDateTo: z.string().optional(),

  // Physical Examinations - structured
  physicalExaminations: z.string().optional(),
  physicalExaminationFindings: z.string().optional(),

  // Allergies - structured
  allergies: z.string().optional(),

  // Family History - structured
  familyHistory: z.string().optional(),
  familyHistoryRelation: z.string().optional(),

  // Investigations/Diagnostics - structured
  diagnosticTestName: z.string().optional(),
  diagnosticResult: z.string().optional(),
  diagnosticDate: z.string().optional(),

  // Procedures - structured
  procedureName: z.string().optional(),
  procedureDate: z.string().optional(),
  procedureStatus: z.string().optional(),
  procedureReason: z.string().optional(),
  procedureOutcome: z.string().optional(),

  // Medications - structured
  medicationName: z.string().optional(),
  medicationDosage: z.string().optional(),
  medicationTiming: z.string().optional(),
  medicationRoute: z.string().optional(),
  medicationMethod: z.string().optional(),
  medicationInstructions: z.string().optional(),

  // Care Plan - structured
  carePlanIntent: z.string().optional(),
  carePlanType: z.string().optional(),
  carePlanGoal: z.string().optional(),
  carePlanDescription: z.string().optional(),
  carePlanNotes: z.string().optional(),

  // Follow-up
  followUpDate: z.string().optional(),
  dischargeInstructions: z.string().optional(),

  // Legacy fields for backward compatibility
  investigations: z.string().optional(),
  vitalSigns: z.string().optional(),
  labResults: z.string().optional(),
  proceduresPerformed: z.string().optional(),
  medicationsList: z.string().optional(),
  carePlan: z.string().optional(),
  recommendations: z.string().optional(),
  diagnosisName: z.string().optional(),
  diagnosisCode: z.string().optional(),
  dischargeSummary: z.string().optional(),
  condition: z.string().optional(),
  treatmentProvided: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface DischargeSummaryFormProps {
  patientId: string;
  doctorId: string;
  consultationId: string; // Now required
  onCancel?: () => void;
  onSuccess?: () => void;
  onRefresh?: () => void;
  initialData?: Partial<FormValues>;
  mode?: "create" | "edit";
  dischargeSummaryId?: string;
}

export function DischargeSummaryForm({
  patientId,
  doctorId,
  consultationId,
  onCancel,
  onSuccess,
  onRefresh,
  initialData,
  mode = "create",
  dischargeSummaryId,
}: DischargeSummaryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      patientId,
      doctorId,
      consultationId,
      admissionDate: initialData?.admissionDate || "",
      dischargeDate: initialData?.dischargeDate || "",

      // Chief Complaints - structured
      chiefComplaint: initialData?.chiefComplaint || "",
      chiefComplaintDateFrom: initialData?.chiefComplaintDateFrom || "",
      chiefComplaintDateTo: initialData?.chiefComplaintDateTo || "",

      // Medical History - structured
      medicalHistory: initialData?.medicalHistory || "",
      medicalHistoryDateFrom: initialData?.medicalHistoryDateFrom || "",
      medicalHistoryDateTo: initialData?.medicalHistoryDateTo || "",

      // Physical Examinations - structured
      physicalExaminations: initialData?.physicalExaminations || "",
      physicalExaminationFindings: initialData?.physicalExaminationFindings || "",

      // Allergies - structured
      allergies: initialData?.allergies || "",

      // Family History - structured
      familyHistory: initialData?.familyHistory || "",
      familyHistoryRelation: initialData?.familyHistoryRelation || "",

      // Investigations/Diagnostics - structured
      diagnosticTestName: initialData?.diagnosticTestName || "",
      diagnosticResult: initialData?.diagnosticResult || "",
      diagnosticDate: initialData?.diagnosticDate || "",

      // Procedures - structured
      procedureName: initialData?.procedureName || "",
      procedureDate: initialData?.procedureDate || "",
      procedureStatus: initialData?.procedureStatus || "COMPLETED",
      procedureReason: initialData?.procedureReason || "",
      procedureOutcome: initialData?.procedureOutcome || "",

      // Medications - structured
      medicationName: initialData?.medicationName || "",
      medicationDosage: initialData?.medicationDosage || "",
      medicationTiming: initialData?.medicationTiming || "",
      medicationRoute: initialData?.medicationRoute || "Oral",
      medicationMethod: initialData?.medicationMethod || "Swallow",
      medicationInstructions: initialData?.medicationInstructions || "",

      // Care Plan - structured
      carePlanIntent: initialData?.carePlanIntent || "plan",
      carePlanType: initialData?.carePlanType || "",
      carePlanGoal: initialData?.carePlanGoal || "",
      carePlanDescription: initialData?.carePlanDescription || "",
      carePlanNotes: initialData?.carePlanNotes || "",

      // Follow-up
      followUpDate: initialData?.followUpDate || "",
      dischargeInstructions: initialData?.dischargeInstructions || "",

      // Legacy fields for backward compatibility
      investigations: initialData?.investigations || "",
      vitalSigns: initialData?.vitalSigns || "",
      labResults: initialData?.labResults || "",
      proceduresPerformed: initialData?.proceduresPerformed || "",
      medicationsList: initialData?.medicationsList || "",
      carePlan: initialData?.carePlan || "",
      recommendations: initialData?.recommendations || "",
      diagnosisName: initialData?.diagnosisName || "",
      diagnosisCode: initialData?.diagnosisCode || "",
      dischargeSummary: initialData?.dischargeSummary || "",
      condition: initialData?.condition || "",
      treatmentProvided: initialData?.treatmentProvided || "",
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // Convert structured form fields to JSON format for database storage
      const payload = {
        ...data,

        // Chief Complaints JSON - structured from form fields
        chiefComplaintsJson: data.chiefComplaint ? JSON.stringify([{
          complaint: data.chiefComplaint,
          recordedDate: new Date().toISOString().split('T')[0],
          dateRange: data.chiefComplaintDateFrom && data.chiefComplaintDateTo ? {
            from: data.chiefComplaintDateFrom,
            to: data.chiefComplaintDateTo,
          } : undefined,
        }]) : null,

        // Medical History JSON - structured from form fields
        medicalHistoriesJson: data.medicalHistory ? JSON.stringify([{
          complaint: data.medicalHistory,
          recordedDate: new Date().toISOString().split('T')[0],
          dateRange: data.medicalHistoryDateFrom && data.medicalHistoryDateTo ? {
            from: data.medicalHistoryDateFrom,
            to: data.medicalHistoryDateTo,
          } : undefined,
        }]) : null,

        // Physical Examinations JSON - structured from form fields
        physicalExaminationsJson: data.physicalExaminations || data.physicalExaminationFindings ? JSON.stringify([{
          observation: data.physicalExaminations || "Physical Examination",
          result: data.physicalExaminationFindings || data.physicalExaminations || "",
        }]) : null,

        // Allergies JSON - structured from form fields
        allergiesJson: data.allergies ? JSON.stringify(
          data.allergies.split(',').map(a => a.trim()).filter(a => a)
        ) : null,

        // Family History JSON - structured from form fields
        familyHistoriesJson: data.familyHistory ? JSON.stringify([{
          condition: data.familyHistory,
          relation: data.familyHistoryRelation || "Unknown",
        }]) : null,

        // Diagnostics JSON - structured from form fields
        diagnosticsJson: data.diagnosticTestName || data.diagnosticResult ? JSON.stringify([{
          testName: data.diagnosticTestName || "Diagnostic Test",
          result: data.diagnosticResult || "Normal",
          date: data.diagnosticDate || new Date().toISOString().split('T')[0],
        }]) : null,

        // Procedures JSON - structured from form fields
        proceduresJson: data.procedureName || data.proceduresPerformed ? JSON.stringify([{
          date: data.procedureDate || new Date().toISOString().split('T')[0],
          status: data.procedureStatus || "COMPLETED",
          procedureReason: data.procedureReason || "Medical procedure",
          outcome: data.procedureOutcome || "Successful",
          procedureName: data.procedureName || data.proceduresPerformed || "Procedure",
        }]) : null,

        // Medications JSON - structured from form fields
        medicationsJson: data.medicationName || data.medicationsList ? JSON.stringify([{
          medicine: data.medicationName || data.medicationsList || "Medication",
          dosage: data.medicationDosage || "As prescribed",
          timing: data.medicationTiming || "As directed",
          route: data.medicationRoute || "Oral",
          method: data.medicationMethod || "Swallow",
          additionalInstructions: data.medicationInstructions || "",
        }]) : null,

        // Care Plan JSON - structured from form fields
        carePlanJson: JSON.stringify({
          intent: data.carePlanIntent || "plan",
          type: data.carePlanType || "Care plan",
          goal: data.carePlanGoal || "Patient recovery",
          description: data.carePlanDescription || data.carePlan || "Follow-up care plan",
          notes: data.carePlanNotes || data.dischargeInstructions || "Follow care instructions",
        }),
      };

      let response;
      if (mode === "edit" && dischargeSummaryId) {
        response = await Fetch.put(
          `/api/discharge-summary/${dischargeSummaryId}`,
          payload,
        );
      } else {
        response = await Fetch.post("/api/discharge-summary", payload);
      }

      if (response.error) {
        throw new Error(response.error);
      }

      toast.success(
        mode === "edit"
          ? "Discharge summary updated successfully"
          : "Discharge summary created successfully",
      );
      form.reset();

      // Call refresh callback if provided
      if (onRefresh) {
        onRefresh();
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error saving discharge summary:", error);
      toast.error("Failed to save discharge summary");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">


        {/* Chief Complaints Section */}
        <Card>
          <CardHeader>
            <CardTitle>Chief Complaints</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="chiefComplaint"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Chief Complaints</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Patient's primary complaints and symptoms"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="chiefComplaintDateFrom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Complaint From Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="chiefComplaintDateTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Complaint To Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Medical History Section */}
        <Card>
          <CardHeader>
            <CardTitle>Medical History</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="medicalHistory"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Medical History</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Patient's past medical conditions and history"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="medicalHistoryDateFrom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>History From Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="medicalHistoryDateTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>History To Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Allergies & Family History Section */}
        <Card>
          <CardHeader>
            <CardTitle>Allergies & Family History</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="allergies"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Allergies</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="List patient allergies separated by commas (e.g., Penicillin, Shellfish, Peanuts)"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="familyHistory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Family History Condition</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Diabetes, Hypertension"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="familyHistoryRelation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relation</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Father, Mother, Sibling"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Physical Examinations Section */}
        <Card>
          <CardHeader>
            <CardTitle>Physical Examinations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="physicalExaminations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Physical Examination Type</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Cardiovascular examination, Respiratory examination"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="physicalExaminationFindings"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Physical Examination Findings</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed findings and observations from physical examination"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Investigations/Diagnostics Section */}
        <Card>
          <CardHeader>
            <CardTitle>Investigations & Diagnostics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="diagnosticTestName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., ECG, Blood Test, X-Ray"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="diagnosticDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="diagnosticResult"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Test Result</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed test results and interpretation"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Legacy field for backward compatibility */}
            <FormField
              control={form.control}
              name="investigations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Investigations (Legacy)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional investigations not covered above"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Procedures Section */}
        <Card>
          <CardHeader>
            <CardTitle>Procedures</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="procedureName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Procedure Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Coronary angiography, Appendectomy"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="procedureDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Procedure Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="procedureStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Procedure Status</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., COMPLETED, IN_PROGRESS"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="procedureOutcome"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Procedure Outcome</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Successful, Complications"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="procedureReason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Procedure Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Reason for performing the procedure"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Legacy field for backward compatibility */}
            <FormField
              control={form.control}
              name="proceduresPerformed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Procedures (Legacy)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional procedures not covered above"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Medications Section */}
        <Card>
          <CardHeader>
            <CardTitle>Medications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="medicationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Medication Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Lisinopril, Metformin"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="medicationDosage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dosage</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 10mg, 500mg"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="medicationTiming"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Timing</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., 1-0-1, Twice daily"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="medicationRoute"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Route</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Oral, IV, IM"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="medicationMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Method</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Swallow, Chew"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="medicationInstructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Instructions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Special instructions for medication administration"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Legacy field for backward compatibility */}
            <FormField
              control={form.control}
              name="medicationsList"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Medications (Legacy)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional medications not covered above"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
        {/* Care Plan Section */}
        <Card>
          <CardHeader>
            <CardTitle>Care Plan & Discharge Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="carePlanIntent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Care Plan Intent</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., plan, order"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="carePlanType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Care Plan Type</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Cardiac rehabilitation, Diabetes management"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="carePlanGoal"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Care Plan Goal</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Improve cardiovascular health, Control blood sugar"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="carePlanDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Care Plan Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed description of the care plan"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="carePlanNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Care Plan Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes and follow-up instructions"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dischargeInstructions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discharge Instructions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Specific instructions for patient discharge"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="followUpDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Follow-up Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Document Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle>Document Upload (Optional)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Upload a PDF discharge summary document that will be included in
              the FHIR bundle when discharge summary is saved.
            </p>
            <AzureFhirUpload
              consultationId={consultationId}
              patientId={patientId}
              bundleType="DischargeSummary"
              title="Upload Discharge Summary Document"
              description="Upload a PDF discharge summary document"
              onUploadSuccess={(_result) => {
                toast.success(
                  "Discharge summary document uploaded successfully. It will be included when discharge summary is saved.",
                );
              }}
              hideWhenFilesExist={false}
            />
            <UploadedFilesList
              consultationId={consultationId}
              bundleType="DischargeSummary"
              title="Uploaded Discharge Summary Documents"
              description="Documents that will be included in the FHIR bundle"
              showOnlyGenerated={false}
              className="mt-4"
            />
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? mode === "edit"
                ? "Updating..."
                : "Creating..."
              : mode === "edit"
                ? "Update Discharge Summary"
                : "Create Discharge Summary"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
