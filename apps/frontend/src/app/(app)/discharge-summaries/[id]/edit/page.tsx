"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { DischargeSummaryForm } from "@/components/discharge-summary/discharge-summary-form";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";

interface DischargeSummary {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId: string | null; // Updated to handle null values from existing records
  content: {
    admissionDate: string;
    dischargeDate: string;
    chiefComplaint: string;
    diagnosisName: string;
    diagnosisCode?: string;
    procedureName?: string;
    procedureDate?: string;
    medicationName?: string;
    medicationInstructions?: string;
    followUpDate?: string;
    dischargeSummary: string;
    dischargeInstructions?: string;
    condition?: string;
    vitalSigns?: string;
    labResults?: string;
    treatmentProvided?: string;
    recommendations?: string;
  };
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  doctor?: {
    id: string;
    user: {
      name: string;
    };
  };
  consultation?: {
    patient: {
      firstName: string;
      lastName: string;
    };
    doctor: {
      user: {
        name: string;
      };
    };
  };
}

export default function EditDischargeSummaryPage() {
  const params = useParams();
  const router = useRouter();
  const [dischargeSummary, setDischargeSummary] =
    useState<DischargeSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      fetchDischargeSummary(params.id as string);
    }
  }, [params?.id]);

  const fetchDischargeSummary = async (id: string) => {
    try {
      setLoading(true);
      const response = await Fetch.get(`/api/discharge-summary/${id}`);
      if (response.error) {
        throw new Error(response.error);
      }
      setDischargeSummary(response as unknown as DischargeSummary);
    } catch (error) {
      console.error("Error fetching discharge summary:", error);
      toast.error("Failed to fetch discharge summary");
      router.push("/discharge-summaries");
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    toast.success("Discharge summary updated successfully");
    router.push(`/discharge-summaries/${params?.id}`);
  };

  const handleCancel = () => {
    router.push(`/discharge-summaries/${params?.id}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading discharge summary...</div>
        </div>
      </div>
    );
  }

  if (!dischargeSummary) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Discharge Summary Not Found
          </h1>
          <Link href="/discharge-summaries">
            <Button>Back to Discharge Summaries</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/discharge-summaries/${params?.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Summary
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Edit Discharge Summary</h1>
          <p className="text-muted-foreground">
            {dischargeSummary.patient
              ? `${dischargeSummary.patient.firstName} ${dischargeSummary.patient.lastName}`
              : dischargeSummary.consultation
                ? `${dischargeSummary.consultation.patient.firstName} ${dischargeSummary.consultation.patient.lastName}`
                : "Unknown Patient"}
          </p>
        </div>
      </div>

      {/* Form */}
      {dischargeSummary.consultationId ? (
        <Card>
          <CardHeader>
            <CardTitle>Update Discharge Summary Details</CardTitle>
          </CardHeader>
          <CardContent>
            <DischargeSummaryForm
              patientId={dischargeSummary.patientId}
              doctorId={dischargeSummary.doctorId}
              consultationId={dischargeSummary.consultationId}
              onSuccess={handleSuccess}
              onCancel={handleCancel}
              mode="edit"
              dischargeSummaryId={dischargeSummary.id}
              initialData={{
                patientId: dischargeSummary.patientId,
                doctorId: dischargeSummary.doctorId,
                consultationId: dischargeSummary.consultationId,
                admissionDate: dischargeSummary.content.admissionDate,
                dischargeDate: dischargeSummary.content.dischargeDate,
                chiefComplaint: dischargeSummary.content.chiefComplaint,
                diagnosisName: dischargeSummary.content.diagnosisName,
                diagnosisCode: dischargeSummary.content.diagnosisCode,
                procedureName: dischargeSummary.content.procedureName,
                procedureDate: dischargeSummary.content.procedureDate,
                medicationName: dischargeSummary.content.medicationName,
                medicationInstructions:
                  dischargeSummary.content.medicationInstructions,
                followUpDate: dischargeSummary.content.followUpDate,
                dischargeSummary: dischargeSummary.content.dischargeSummary,
                dischargeInstructions:
                  dischargeSummary.content.dischargeInstructions,
                condition: dischargeSummary.content.condition,
                vitalSigns: dischargeSummary.content.vitalSigns,
                labResults: dischargeSummary.content.labResults,
                treatmentProvided: dischargeSummary.content.treatmentProvided,
                recommendations: dischargeSummary.content.recommendations,
              }}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-muted-foreground">
              <h3 className="text-lg font-semibold mb-2">
                Legacy Discharge Summary
              </h3>
              <p className="mb-4">
                This discharge summary was created before consultation linking
                was required. It cannot be edited using the new form that
                requires a consultation.
              </p>
              <p className="text-sm">
                To edit this summary, please contact your system administrator
                or create a new discharge summary with proper consultation
                linking.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
