import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { generateDischargeSummaryTemplate, DischargeSummaryData } from "@/lib/fhir-templates/discharge-summary-template";
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate and Save Discharge Summary FHIR Bundle Template
 * POST /api/fhir/discharge-summary-template
 */
export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const {
      dischargeSummaryId,
      consultationId,
      patientId,
      organizationId,
    } = body;

    // Validate required fields
    if (!dischargeSummaryId || !consultationId || !patientId || !organizationId) {
      return NextResponse.json(
        {
          error: "Missing required fields: dischargeSummaryId, consultationId, patientId, organizationId",
        },
        { status: 400 },
      );
    }

    // Fetch the discharge summary document reference with all related data
    const dischargeSummary = await db.documentReference.findFirst({
      where: {
        id: dischargeSummaryId,
        type: "discharge-summary",
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            gender: true,
            dateOfBirth: true,
            phone: true,
            email: true,
            address: true,
            city: true,
            state: true,
            pincode: true,
          },
        },
        doctor: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            startTime: true,
            endTime: true,
            status: true,
          },
        },
      },
    });

    if (!dischargeSummary) {
      return NextResponse.json(
        { error: "Discharge summary not found" },
        { status: 404 },
      );
    }

    // Parse JSON fields from the discharge summary
    const chiefComplaints = dischargeSummary.chiefComplaintsJson 
      ? JSON.parse(dischargeSummary.chiefComplaintsJson as string) 
      : [];
    const physicalExaminations = dischargeSummary.physicalExaminationsJson 
      ? JSON.parse(dischargeSummary.physicalExaminationsJson as string) 
      : [];
    const allergies = dischargeSummary.allergiesJson 
      ? JSON.parse(dischargeSummary.allergiesJson as string) 
      : [];
    const medicalHistories = dischargeSummary.medicalHistoriesJson 
      ? JSON.parse(dischargeSummary.medicalHistoriesJson as string) 
      : [];
    const familyHistories = dischargeSummary.familyHistoriesJson 
      ? JSON.parse(dischargeSummary.familyHistoriesJson as string) 
      : [];
    const medications = dischargeSummary.medicationsJson 
      ? JSON.parse(dischargeSummary.medicationsJson as string) 
      : [];
    const diagnostics = dischargeSummary.diagnosticsJson 
      ? JSON.parse(dischargeSummary.diagnosticsJson as string) 
      : [];
    const procedures = dischargeSummary.proceduresJson 
      ? JSON.parse(dischargeSummary.proceduresJson as string) 
      : [];
    const carePlan = dischargeSummary.carePlanJson 
      ? JSON.parse(dischargeSummary.carePlanJson as string) 
      : null;

    // Prepare data for the template
    const templateData: DischargeSummaryData = {
      // Patient information
      patientId: dischargeSummary.patient.id,
      patientFirstName: dischargeSummary.patient.firstName,
      patientLastName: dischargeSummary.patient.lastName,
      patientGender: dischargeSummary.patient.gender,
      patientDateOfBirth: dischargeSummary.patient.dateOfBirth.toISOString().split('T')[0],
      patientPhone: dischargeSummary.patient.phone,
      patientEmail: dischargeSummary.patient.email || undefined,
      patientAddress: dischargeSummary.patient.address || undefined,
      patientCity: dischargeSummary.patient.city || undefined,
      patientState: dischargeSummary.patient.state || undefined,
      patientPincode: dischargeSummary.patient.pincode || undefined,

      // Doctor information
      doctorId: dischargeSummary.doctor.id,
      doctorName: dischargeSummary.doctor.user.name || "Unknown Doctor",
      doctorQualification: dischargeSummary.doctor.qualification || undefined,

      // Organization information
      organizationId: dischargeSummary.organization.id,
      organizationName: dischargeSummary.organization.name,

      // Encounter information
      encounterId: dischargeSummary.consultation?.id || uuidv4(),
      encounterStartDate: dischargeSummary.consultation?.consultationDate.toISOString() || new Date().toISOString(),
      encounterEndDate: dischargeSummary.consultation?.consultationDate.toISOString() || new Date().toISOString(),
      encounterStatus: "finished",

      // Clinical data
      chiefComplaints,
      physicalExaminations,
      allergies,
      medicalHistories,
      familyHistories,
      medications,
      diagnostics,
      procedures,
      carePlan,

      // Document reference
      documentTitle: dischargeSummary.description || "Discharge Summary Document",
      // Note: documentBase64 and documentUrl will be handled separately if needed
    };

    // Generate the FHIR bundle using the template
    const fhirBundle = generateDischargeSummaryTemplate(templateData);

    // Save the bundle to the FhirBundle table
    const savedBundle = await db.fhirBundle.create({
      data: {
        bundleId: fhirBundle.id,
        bundleType: "DischargeSummary",
        bundleJson: fhirBundle,
        consultationId: consultationId,
        patientId: patientId,
        organizationId: organizationId,
        status: "active",
      },
    });

    console.log("✅ Discharge summary FHIR bundle created and saved:", {
      bundleId: savedBundle.bundleId,
      dischargeSummaryId,
      consultationId,
      patientId,
    });

    return NextResponse.json({
      success: true,
      bundleId: savedBundle.bundleId,
      fhirBundleId: savedBundle.id,
      message: "Discharge summary FHIR bundle created successfully",
    });

  } catch (error) {
    console.error("❌ Error creating discharge summary FHIR bundle:", error);
    return NextResponse.json(
      {
        error: "Failed to create discharge summary FHIR bundle",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
