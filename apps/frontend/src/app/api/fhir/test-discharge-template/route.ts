import { NextResponse } from "next/server";
import { generateDischargeSummaryTemplate, type DischargeSummaryData } from "@/lib/fhir-templates/discharge-summary-template";

/**
 * Test route for the discharge summary template
 * GET /api/fhir/test-discharge-template
 */
export async function GET() {
  try {
    // Sample data for testing
    const sampleData: DischargeSummaryData = {
      // Patient information
      patientId: "patient-123",
      patientFirstName: "John",
      patientLastName: "Doe",
      patientGender: "male",
      patientDateOfBirth: "1980-01-15",
      patientPhone: "+**********",
      patientEmail: "<EMAIL>",
      patientAddress: "123 Main St",
      patientCity: "Anytown",
      patientState: "CA",
      patientPincode: "12345",

      // Doctor information
      doctorId: "doctor-456",
      doctorName: "Dr. <PERSON>",
      doctorQualification: "MD, Internal Medicine",

      // Organization information
      organizationId: "org-789",
      organizationName: "General Hospital",
      organizationPhone: "+**********",
      organizationEmail: "<EMAIL>",

      // Encounter information
      encounterId: "encounter-101",
      encounterStartDate: "2025-08-01T10:00:00Z",
      encounterEndDate: "2025-08-05T14:00:00Z",
      encounterStatus: "finished",

      // Clinical data
      chiefComplaints: [
        {
          complaint: "Chest pain and shortness of breath",
          recordedDate: "2025-08-01",
          dateRange: { from: "2025-07-30", to: "2025-08-01" }
        }
      ],
      physicalExaminations: [
        {
          observation: "Cardiovascular examination",
          result: "Regular rate and rhythm, no murmurs"
        }
      ],
      allergies: ["Penicillin", "Shellfish"],
      medicalHistories: [
        {
          complaint: "Hypertension",
          recordedDate: "2020-01-01",
          dateRange: { from: "2020-01-01", to: "2025-08-01" }
        }
      ],
      familyHistories: [
        {
          condition: "Diabetes",
          relation: "Father"
        }
      ],
      medications: [
        {
          medicine: "Lisinopril",
          dosage: "10mg",
          timing: "1-0-0",
          route: "Oral",
          method: "Swallow",
          additionalInstructions: "Take with food"
        }
      ],
      diagnostics: [
        {
          testName: "ECG",
          result: "Normal sinus rhythm",
          date: "2025-08-02"
        }
      ],
      procedures: [
        {
          date: "2025-08-03",
          status: "COMPLETED",
          procedureReason: "Cardiac catheterization",
          outcome: "Successful",
          procedureName: "Coronary angiography"
        }
      ],
      carePlan: {
        intent: "plan",
        type: "Cardiac rehabilitation",
        goal: "Improve cardiovascular health",
        description: "6-week cardiac rehabilitation program",
        notes: "Patient to follow up in 2 weeks"
      },

      // Document reference
      documentTitle: "Discharge Summary - John Doe",
      documentSize: 1024
    };

    // Generate the FHIR bundle
    const fhirBundle = generateDischargeSummaryTemplate(sampleData);

    return NextResponse.json({
      success: true,
      message: "Discharge summary template generated successfully",
      bundle: fhirBundle,
      bundleId: fhirBundle.id,
      entryCount: fhirBundle.entry.length,
      entryTypes: fhirBundle.entry.map((entry: any) => entry.resource.resourceType),
    });

  } catch (error) {
    console.error("❌ Error testing discharge summary template:", error);
    return NextResponse.json(
      {
        error: "Failed to generate discharge summary template",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
