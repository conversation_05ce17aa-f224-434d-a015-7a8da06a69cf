import { NextRequest, NextResponse } from "next/server";
import {
  generateResource,
  ResourceType,
} from "@workspace/fhir-observation/src";
import { db } from "@/lib/db";
import { convertPdfToBase64 } from "@/lib/fhir/binary-utils";
// import { prisma } from "@/lib/prisma";

/**
 * FHIR Discharge Summary Bundle Generator API
 *
 * This API endpoint generates a FHIR Discharge Summary bundle based on the request parameters.
 *
 * GET /api/fhir/bundles/discharge-summary?patientName=John%20Doe&...
 */
async function generateDischargeSummaryPdfBase64(consultationId: string): Promise<string> {
  try {
    console.log("🔄 Generating discharge summary PDF for consultation:", consultationId);

    // Import required modules
    const React = await import("react");
    const { prisma } = await import("@/lib/prisma");
    const { generatePdfBuffer } = await import("@/services/pdf-generation/pdf-generator");

    // Fetch consultation data (similar to discharge-summaries route)
    const consultation = await prisma.consultation.findUnique({
      where: { id: consultationId },
      include: {
        patient: {
          include: {
            abhaProfile: true,
          },
        },
        doctor: {
          include: {
            user: true,
          },
        },
        organization: true,
        branch: {
          include: {
            organization: true,
          },
        },
        vitals: true,
        prescriptions: true,
        clinicalNotes: true,
        DiagnosticReport: true,
        Immunization: true,
        DocumentReference: true,
      },
    });
    console.log("Fetched consultation data:", consultation);

    if (!consultation) {
      throw new Error(`Consultation not found: ${consultationId}`);
    }

    // Generate PDF using DischargeSummaryPDF template
    const { DischargeSummaryPDF } = await import("@/services/pdf-templates/DischargeSummaryPDF");
    const pdfBuffer = await generatePdfBuffer(
      React.createElement(DischargeSummaryPDF, {
        consultationData: consultation as any,
      }),
    );

    console.log("✅ Discharge summary PDF generated successfully, size:", pdfBuffer.length, "bytes");

    // Convert to base64
    return convertPdfToBase64(pdfBuffer);

  } catch (error) {
    console.error("❌ Error generating discharge summary PDF:", error);

    // Instead of returning placeholder, throw error to prevent bundle creation with bad PDF
    throw new Error(`Failed to generate discharge summary PDF: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;

    // Extract all parameters from the query string
    const params: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      params[key] = value;
    });
    const consultationId = params.consultationId;
    console.log("Consultation ID:", consultationId);
    //   const consultation = await prisma.consultation.findUnique({
    //   where: { id: consultationId },
    //   include: {
    //     patient: {
    //       include: {
    //         abhaProfile: true,
    //       },
    //     },
    //     doctor: {
    //       include: {
    //         user: true,
    //       },
    //     },
    //     organization: true,
    //     branch: {
    //       include: {
    //         organization: true,
    //       },
    //     },
    //     vitals: true,
    //     prescriptions: true,
    //     clinicalNotes: true,
    //     DiagnosticReport: true,
    //     Immunization: true,
    //     DocumentReference: true,
    //   },
    // });

    // Debug: Log received parameters
    console.log("Received parameters:", params);
    console.log("Document URL received:", params.documentUrl);
    console.log("Organization Name received:", params.organizationName);
    console.log("Consultation ID received:", params.consultationId);

    // Fetch uploaded documents if consultationId is provided
    if (params.consultationId) {
      console.log(
        `🔍 [DISCHARGE] Fetching uploaded documents for consultation: ${params.consultationId}`,
      );

      const uploadedDocuments = await db.fhirBundle.findMany({
        where: {
          consultationId: params.consultationId,
          bundleType: "DischargeSummary",
          status: "processed",
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      console.log(
        `🎯 [DISCHARGE] Found ${uploadedDocuments.length} uploaded discharge summary documents`,
      );

      // Process the most recent uploaded document
      // if (uploadedDocuments.length > 0) {
      //   const uploadedDoc = uploadedDocuments[0];
      //   try {
      //     const docBundle = uploadedDoc.bundleJson as any;
      //     if (docBundle?.entry) {
      //       const docRefEntry = docBundle.entry.find(
      //         (entry: any) =>
      //           entry.resource?.resourceType === "DocumentReference",
      //       );

      //       if (docRefEntry?.resource) {
      //         const base64Data =
      //           docRefEntry.resource.content?.[0]?.attachment?.data;
      //         if (base64Data) {
      //           dynamicDocumentUrl = base64Data;
      //           console.log(
      //             `✅ [DISCHARGE] Extracted base64 document data (${base64Data.length} characters)`,
      //           );
      //         }
      //       }
      //     }
      //   } catch (error) {
      //     console.error(
      //       `❌ [DISCHARGE] Error processing uploaded document:`,
      //       error,
      //     );
      //   }
      // }
    }
    const documentUrl = await generateDischargeSummaryPdfBase64(params.consultationId);
    

    // Set default values if not provided and map to expected field names
    const mergedParams = {
      // Composition details
      compositionIdentifier:
        params.compositionIdentifier || "645bb0c3-ff7e-4123-bef5-3852a4784813",
      compositionDate: new Date().toISOString(),
      compositionTitle: params.compositionTitle || "Discharge Summary",

      // Patient details (required format)
      patientID: params.patientId || "22-7225-4829-5255",
      patientFirstName: (params.patientName || "John Doe").split(" ")[0],
      patientLastName:
        (params.patientName || "John Doe").split(" ").slice(1).join(" ") ||
        "Doe",
      patientGender: params.patientGender || "male",
      patientBirthDate: params.patientBirthDate || "1981-01-12",
      patientPhone: params.patientPhone || "+************",
      patientEmail: params.patientEmail || "<EMAIL>",
      patientAddress: params.patientAddress || "123 Main St",
      patientCity: params.patientCity || "City",
      patientState: params.patientState || "State",
      patientPostalCode: params.patientPostalCode || "12345",
      patientCountry: params.patientCountry || "India",

      // Practitioner details (required format)
      practitionerID: params.practitionerId || "21-1521-3828-3227",
      practitionerName: params.practitionerName || "Dr. Smith",
      practitionerQualification: params.practitionerQualification || "MBBS",

      // Organization details (required format) - use actual data when available
      organizationID: params.organizationId || "4567878",
      organizationName: params.organizationName || "Healthcare Organization",
      organizationPhone: params.organizationPhone || "+91 243 2634 1234",
      organizationEmail: params.organizationEmail || "<EMAIL>",
      organizationAddress: params.organizationAddress || "Hospital Address",
      organizationCity: params.organizationCity || "City",
      organizationState: params.organizationState || "State",
      organizationPostalCode: params.organizationPostalCode || "12345",
      organizationCountry: params.organizationCountry || "India",

      // Encounter details (required fields with correct names)
      encounterID: params.encounterId || "encounter-" + Date.now(),
      encounterAdmissionDate:
        params.admissionDate ||
        new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      encounterDischargeDate: params.dischargeDate || new Date().toISOString(),
      encounterType: params.encounterType || "inpatient",
      encounterClass: params.encounterClass || "IMP",
      encounterClassDisplay:
        params.encounterClassDisplay || "inpatient encounter",

      // Admission details (required field)
      admissionDetails:
        params.admissionDetails ||
        `Patient admitted on ${params.admissionDate || new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split("T")[0]} with chief complaint: ${params.chiefComplaint || "Chest pain and shortness of breath"}`,

      // Discharge Instructions (required field)
      dischargeInstructions:
        params.dischargeInstructions ||
        "Follow up with primary care physician in 1-2 weeks. Take medications as prescribed. Return to emergency department if symptoms worsen.",

      // Clinical details
      chiefComplaint:
        params.chiefComplaint || "Chest pain and shortness of breath",
      chiefComplaintCode: params.chiefComplaintCode || "29857009",

      diagnosisName: params.diagnosisName || "Acute myocardial infarction",
      diagnosisCode: params.diagnosisCode || "57054005",

      // Condition details (required for FHIR condition resource)
      // These are the field names expected by the discharge summary strategy input interface
      conditionCode: params.diagnosisCode || "57054005",
      conditionDisplay: params.diagnosisName || "Acute myocardial infarction",
      conditionText: params.diagnosisName || "Acute myocardial infarction",
      conditionClinicalStatus: params.conditionClinicalStatus || "active",
      conditionClinicalStatusDisplay:
        params.conditionClinicalStatusDisplay || "Active",
      conditionVerificationStatus:
        params.conditionVerificationStatus || "confirmed",
      conditionVerificationStatusDisplay:
        params.conditionVerificationStatusDisplay || "Confirmed",
      conditionRecordedDate:
        params.conditionRecordedDate || new Date().toISOString(),
      conditionOnsetDate:
        params.conditionOnsetDate ||
        params.admissionDate ||
        new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      conditionCategory: params.conditionCategory || "encounter-diagnosis",
      conditionCategoryDisplay:
        params.conditionCategoryDisplay || "Encounter Diagnosis",
      conditionSeverityCode: params.conditionSeverityCode || "24484000",
      conditionSeverity: params.conditionSeverity || "Severe",

      // Procedure details
      procedureCode: params.procedureCode || "415070008",
      procedureDisplay:
        params.procedureName || "Percutaneous coronary intervention",
      procedureText:
        params.procedureName || "Percutaneous coronary intervention",
      procedureDate:
        params.procedureDate ||
        new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      procedureStatus: params.procedureStatus || "completed",
      procedureCategory: params.procedureCategory || "387713003",
      procedureCategoryDisplay:
        params.procedureCategoryDisplay || "Surgical procedure",
      procedureOutcome: params.procedureOutcome || "Successful procedure",

      // Medication details
      medicationCode: params.medicationCode || "27658006",
      medicationDisplay: params.medicationName || "Aspirin 81mg",
      medicationText: params.medicationName || "Aspirin 81mg",
      medicationAuthoredOn:
        params.medicationAuthoredOn || new Date().toISOString(),
      medicationStatus: params.medicationStatus || "active",
      medicationIntent: params.medicationIntent || "order",
      dosageText: params.medicationInstructions || "Take one tablet daily",
      dosageRouteCode: params.dosageRouteCode || "26643006",
      dosageRoute: params.dosageRoute || "Oral",
      dosageMethodCode: params.dosageMethodCode || "421521009",
      dosageMethod: params.dosageMethod || "Swallow",
      dosageFrequency: params.dosageFrequency || "1",
      dosagePeriodValue: params.dosagePeriodValue || "1",
      dosagePeriodUnit: params.dosagePeriodUnit || "d",
      dosageAsNeeded: params.dosageAsNeeded || "false",
      dosageAsNeededCode: params.dosageAsNeededCode || "",
      dosageAsNeededReason: params.dosageAsNeededReason || "",

      // Observation details (vital signs)
      observationCode: params.observationCode || "8867-4",
      observationDisplay: params.observationDisplay || "Heart rate",
      observationText: params.observationText || "Heart rate",
      observationEffectiveDateTime:
        params.observationEffectiveDateTime || new Date().toISOString(),
      observationValueQuantity: params.observationValueQuantity || "",
      observationValueUnit: params.observationValueUnit || "beats/min",
      observationValueSystem:
        params.observationValueSystem || "http://unitsofmeasure.org",
      observationValueCode: params.observationValueCode || "/min",
      
      carePlanIntent: params.carePlanIntent || "plan",
      carePlanType: params.carePlanType || "Regular check up",
      carePlanGoal: params.carePlanGoal || "Patient care",
      carePlanDescription: params.carePlanDescription || "Standard care plan",
      carePlanNotes: params.carePlanNotes || "Follow standard care protocols",
      carePlanJson: params.carePlanJson || JSON.stringify({
        intent: params.carePlanIntent || "plan",
        type: params.carePlanType || "Regular check up",
        goal: params.carePlanGoal || "Patient care",
        description: params.carePlanDescription || "Standard care plan",
        notes: params.carePlanNotes || "Follow standard care protocols",
      }),

      followUpDate:
        params.followUpDate ||
        new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0],

      dischargeSummary:
        params.dischargeSummary ||
        "Patient admitted with acute chest pain and shortness of breath. Diagnosed with acute myocardial infarction. Underwent percutaneous coronary intervention. Condition improved and patient is stable for discharge.",

      // Additional fields with defaults
      // reportCode: params.reportCode || "11490-0",
      reportDisplay: params.reportDisplay || "Discharge summary",
      // reportText:
      //   params.reportText ||
      //   params.dischargeSummary ||
      //   "Discharge summary text",
      reportEffectiveDateTime:
        params.reportEffectiveDateTime || new Date().toISOString(),
      reportIssuedDateTime:
        params.reportIssuedDateTime || new Date().toISOString(),
      // reportStatus: params.reportStatus || "final",
      // reportCategory: params.reportCategory || "11490-0",
      reportCategoryDisplay:
        params.reportCategoryDisplay || "Discharge summary",
      reportConclusion:
        params.reportConclusion ||
        params.dischargeSummary ||
        "Patient discharged in stable condition",

      appointmentDescription:
        params.appointmentDescription || "Follow-up appointment",
      appointmentStart:
        params.appointmentStart ||
        new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      appointmentEnd:
        params.appointmentEnd ||
        new Date(
          Date.now() + 14 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000,
        ).toISOString(),
      appointmentCreated: params.appointmentCreated || new Date().toISOString(),

      documentTitle: params.documentTitle || "Discharge Summary",
      documentDescription:
        params.documentDescription || "Patient discharge summary document",
      documentCreated: params.documentCreated || new Date().toISOString(),
      documentContentType: params.documentContentType || "application/pdf",
      // Only include documentUrl if it's actually provided (not the example URL)
      // documentUrl: dynamicDocumentUrl || "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",
      documentUrl: documentUrl,
      ...params,
    };

    // Debug: Log merged parameters
    console.log(
      "Full merged parameters object:",
      Object.keys(mergedParams).length,
      "keys",
    );
    console.log("Organization and Document fields in merged params:", {
      organizationName: mergedParams.organizationName,
      documentUrl: mergedParams.documentUrl ? "PROVIDED" : "NOT_PROVIDED",
      documentTitle: mergedParams.documentTitle,
    });
    console.log("Condition fields in merged params:", {
      conditionCode: mergedParams.conditionCode,
      conditionDisplay: mergedParams.conditionDisplay,
      conditionText: mergedParams.conditionText,
      conditionClinicalStatus: mergedParams.conditionClinicalStatus,
      conditionClinicalStatusDisplay:
        mergedParams.conditionClinicalStatusDisplay,
      conditionVerificationStatus: mergedParams.conditionVerificationStatus,
      conditionVerificationStatusDisplay:
        mergedParams.conditionVerificationStatusDisplay,
    });

    // Generate the Discharge Summary bundle
    const bundleString = await generateResource(
      ResourceType.REPORT_DISCHARGE_SUMMARY,
      mergedParams,
    );
    const bundle = JSON.parse(bundleString);

    // Fix DocumentReference if we have base64 data URL
    if (
      mergedParams.documentUrl &&
      mergedParams.documentUrl.startsWith("data:application/pdf;base64,")
    ) {
      console.log("🔧 Fixing DocumentReference with base64 data");

      // Extract base64 data from data URL
      const base64Data = mergedParams.documentUrl.replace(
        "data:application/pdf;base64,",
        "",
      );

      // Find and update DocumentReference entry
      if (bundle.entry) {
        const docRefEntry = bundle.entry.find(
          (entry: any) => entry.resource?.resourceType === "DocumentReference",
        );

        if (docRefEntry?.resource) {
          console.log(
            "📄 Updating DocumentReference attachment with base64 data",
          );

          // Update the attachment to use base64 data instead of URL
          if (docRefEntry.resource.content?.[0]?.attachment) {
            docRefEntry.resource.content[0].attachment = {
              ...docRefEntry.resource.content[0].attachment,
              data: base64Data,
              contentType: "application/pdf",
              title: mergedParams.documentTitle || "Discharge Summary",
              // Remove URL since we're using base64 data
            };

            console.log("✅ DocumentReference updated with base64 data");
          }
        }
      }
    }

    return NextResponse.json(bundle, { status: 200 });
  } catch (error) {
    console.error("Error generating Discharge Summary:", error);
    return NextResponse.json(
      {
        error: `Failed to generate Discharge Summary: ${error instanceof Error ? error.message : String(error)}`,
      },
      { status: 500 },
    );
  }
}
