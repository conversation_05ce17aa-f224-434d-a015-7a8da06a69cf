import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getUserFromRequestCookies } from "@/lib/auth-cookies";
import { db } from "@/lib/db";

// BACKUP NOTE: This file was modified to match upload-discharge-summary route structure
// Original version can be restored from git history if needed

// Schema for creating a discharge summary
const createDischargeSummarySchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().min(1, "Consultation ID is required"),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),
  organizationId: z.string().optional(),

  // Chief Complaints
  chiefComplaint: z.string().optional(),

  // Medical History
  medicalHistory: z.string().optional(),

  // Investigations
  investigations: z.string().optional(),
  vitalSigns: z.string().optional(),
  labResults: z.string().optional(),

  // Procedures
  procedureName: z.string().optional(),
  procedureCode: z.string().optional(),
  procedureDate: z.string().optional(),
  proceduresPerformed: z.string().optional(),

  // Medications
  medicationName: z.string().optional(),
  medicationCode: z.string().optional(),
  medicationInstructions: z.string().optional(),
  medicationsList: z.string().optional(),

  // Care Plan
  carePlan: z.string().optional(),
  followUpDate: z.string().optional(),
  dischargeInstructions: z.string().optional(),
  recommendations: z.string().optional(),

  // Legacy fields
  diagnosisName: z.string().optional(),
  diagnosisCode: z.string().optional(),
  dischargeSummary: z.string().optional(),
  condition: z.string().optional(),
  treatmentProvided: z.string().optional(),
  allergies: z.string().optional(),
  physicalExaminations: z.string().optional(),
  allergiesJson: z.string().optional(),
  physicalExaminationsJson: z.string().optional(),
  medicalHistoriesJson: z.string().optional(),
  familyHistoriesJson: z.string().optional(),
  medicationsJson: z.string().optional(),
  diagnosticsJson: z.string().optional(),
  proceduresJson: z.string().optional(),
  carePlanJson: z.string().optional(),
  chiefComplaintsJson: z.string().optional(), 
  
});

// GET /api/discharge-summary
export async function GET(req: NextRequest) {
  try {
    const user = getUserFromRequestCookies(req);

    const { searchParams } = new URL(req.url);
    const patientId = searchParams?.get("patientId");
    const consultationId = searchParams?.get("consultationId");

    let dischargeSummaries;
    if (patientId) {
      dischargeSummaries = await db.documentReference.findMany({
        where: {
          patientId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } else if (consultationId) {
      dischargeSummaries = await db.documentReference.findMany({
        where: {
          consultationId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } else {
      // Get all discharge summaries for the user's organization
      if (!user.organizationId) {
        return NextResponse.json(
          { error: "User has no organization" },
          { status: 400 },
        );
      }

      dischargeSummaries = await db.documentReference.findMany({
        where: {
          organizationId: user.organizationId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                  gender: true,
                  dateOfBirth: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    }
    // console.log("Discharge summaries:", dischargeSummaries.length);

    return NextResponse.json(dischargeSummaries);
  } catch (error) {
    console.error("Error fetching discharge summaries:", error);
    return NextResponse.json(
      { error: "Failed to fetch discharge summaries" },
      { status: 500 },
    );
  }
}

// POST /api/discharge-summary - Updated with better error handling
export async function POST(req: NextRequest) {
  console.log("=== DISCHARGE SUMMARY API STARTING ===");

  try {
    // Get user info from cookies (same pattern as other working APIs)
    const userInfoCookie = req.cookies.get("user-info");
    const sessionToken = req.cookies.get("session-token");

    console.log("User-info cookie:", userInfoCookie);
    console.log("Session-token cookie:", sessionToken);

    if (!userInfoCookie || !sessionToken) {
      console.log("No user info or session token found");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse the user info
    let userInfo;
    try {
      userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
      console.log("Parsed user info:", userInfo);
    } catch (error) {
      console.log("Error parsing user info:", error);
      return NextResponse.json({ error: "Invalid session" }, { status: 401 });
    }

    const email = userInfo.email;
    const organizationId = userInfo.organizationId;

    if (!organizationId || !email) {
      console.log("No organization ID or email found in user info");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("User authenticated:", { email, organizationId });

    const json = await req.json();
    console.log(
      "Received discharge summary data:",
      JSON.stringify(json, null, 2),
    );

    try {
      const validatedData = createDischargeSummarySchema.parse(json);
      console.log(
        "Validated discharge summary data:",
        JSON.stringify(validatedData, null, 2),
      );
    } catch (validationError) {
      console.error("Validation error:", validationError);
      return NextResponse.json(
        { error: "Validation failed", details: validationError },
        { status: 400 },
      );
    }

    const validatedData = createDischargeSummarySchema.parse(json);

    // Organization ID is already validated above
    console.log("User organization ID:", organizationId);

    // Validate that the consultation exists and belongs to the correct patient and doctor
    const consultation = await db.consultation.findFirst({
      where: {
        id: validatedData.consultationId,
        patientId: validatedData.patientId,
        doctorId: validatedData.doctorId,
        organizationId: organizationId,
      },
    });

    if (!consultation) {
      return NextResponse.json(
        {
          error:
            "Invalid consultation: Consultation not found or does not match the selected patient and doctor",
        },
        { status: 400 },
      );
    }

    // Check if a discharge summary already exists for this consultation
    const existingDischargeSummary = await db.documentReference.findFirst({
      where: {
        consultationId: validatedData.consultationId,
        type: "discharge-summary",
      },
    });

    if (existingDischargeSummary) {
      return NextResponse.json(
        {
          error: "A discharge summary already exists for this consultation",
        },
        { status: 400 },
      );
    }

    // Create the discharge summary as a DocumentReference
    const dischargeSummary = await db.documentReference.create({
      data: {
        patientId: validatedData.patientId,
        doctorId: validatedData.doctorId,
        organizationId: organizationId,
        consultationId: validatedData.consultationId,
        status: "current",
        docStatus: "final",
        type: "discharge-summary",
        typeDisplay: "Discharge Summary",
        category: "clinical",
        categoryDisplay: "Clinical Document",
        subject: `Discharge Summary for Patient`,
        date: validatedData.dischargeDate
          ? new Date(validatedData.dischargeDate)
          : new Date(),
        author: userInfo.name || email || "Unknown",
        description: "Discharge Summary Document",

        // Store structured JSON data for FHIR compliance
        chiefComplaintsJson: (validatedData as any).chiefComplaintsJson,
        physicalExaminationsJson: (validatedData as any).physicalExaminationsJson,
        allergiesJson: (validatedData as any).allergiesJson,
        medicalHistoriesJson: (validatedData as any).medicalHistoriesJson,
        familyHistoriesJson: (validatedData as any).familyHistoriesJson,
        medicationsJson: (validatedData as any).medicationsJson,
        diagnosticsJson: (validatedData as any).diagnosticsJson,
        proceduresJson: (validatedData as any).proceduresJson,
        carePlanJson: (validatedData as any).carePlanJson,

        content: {
          admissionDate: validatedData.admissionDate,
          dischargeDate: validatedData.dischargeDate,

          // Chief Complaints
          chiefComplaint: validatedData.chiefComplaint,

          // Medical History
          medicalHistory: validatedData.medicalHistory,

          // Investigations
          investigations: validatedData.investigations,
          vitalSigns: validatedData.vitalSigns,
          labResults: validatedData.labResults,

          // Procedures
          procedureName: validatedData.procedureName,
          procedureCode: validatedData.procedureCode,
          procedureDate: validatedData.procedureDate,
          proceduresPerformed: validatedData.proceduresPerformed,

          // Medications
          medicationName: validatedData.medicationName,
          medicationCode: validatedData.medicationCode,
          medicationInstructions: validatedData.medicationInstructions,
          medicationsList: validatedData.medicationsList,

          // Care Plan
          carePlan: validatedData.carePlan,
          followUpDate: validatedData.followUpDate,
          dischargeInstructions: validatedData.dischargeInstructions,
          recommendations: validatedData.recommendations,

          // Legacy fields
          diagnosisName: validatedData.diagnosisName,
          diagnosisCode: validatedData.diagnosisCode,
          dischargeSummary: validatedData.dischargeSummary,
          condition: validatedData.condition,
          treatmentProvided: validatedData.treatmentProvided,

          // New structured fields
          allergies: (validatedData as any).allergies,
          physicalExaminations: (validatedData as any).physicalExaminations,
        },
      } as any,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            gender: true,
            dateOfBirth: true,
          },
        },
        doctor: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        consultation: {
          include: {
            patient: {
              select: {
                firstName: true,
                lastName: true,
                gender: true,
                dateOfBirth: true,
              },
            },
            doctor: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Following OPConsult pattern: Call external bundle API and store FHIR bundle
    // try {
    //   const {
    //     generatePdfForBundleType,
    //     getUploadedDocumentsBase64,
    //     buildDischargeSummaryBundlePayload,
    //     callExternalBundleApi,
    //     storeFhirBundleInDatabase,
    //   } = await import("@/lib/bundle-utils");

    //   // Fetch full consultation data for bundle generation
    //   const consultation = await db.consultation.findUnique({
    //     where: {
    //       id: validatedData.consultationId,
    //       organizationId,
    //     },
    //     include: {
    //       patient: true,
    //       doctor: {
    //         include: {
    //           user: true,
    //         },
    //       },
    //       organization: true,
    //       branch: {
    //         include: {
    //           organization: true,
    //         },
    //       },
    //       clinicalNotes: {
    //         orderBy: {
    //           createdAt: "desc",
    //         },
    //         take: 1,
    //       },
    //       vitals: {
    //         orderBy: {
    //           recordedAt: "desc",
    //         },
    //         take: 1,
    //       },
    //       prescriptions: {
    //         include: {
    //           items: true,
    //         },
    //         orderBy: {
    //           createdAt: "desc",
    //         },
    //         take: 1,
    //       },
    //       Procedure: true,
    //       DiagnosticReport: true,
    //     },
    //   });

    //   if (consultation) {
    //     // Check if DischargeSummary bundle already exists for this consultation
    //     const existingBundle = await db.fhirBundle.findFirst({
    //       where: {
    //         consultationId: validatedData.consultationId,
    //         bundleType: "DischargeSummary",
    //         organizationId,
    //       },
    //     });

    //     if (existingBundle) {
    //       console.log("⚠️ DischargeSummary bundle already exists, skipping external API call", {
    //         consultationId: validatedData.consultationId,
    //         existingBundleId: existingBundle.bundleId,
    //       });
    //     } else {
    //       // Generate discharge summary PDF specifically for this bundle
    //       const generatedPdfBase64 = await generatePdfForBundleType(
    //         validatedData.consultationId,
    //         "DischargeSummary"
    //       );

    //       // Get uploaded documents (if any)
    //       const uploadedDocuments = await getUploadedDocumentsBase64(
    //         validatedData.consultationId,
    //         'DischargeSummary'
    //       );

    //       // Build the discharge summary bundle payload
    //       const bundlePayload = buildDischargeSummaryBundlePayload(
    //         consultation,
    //         generatedPdfBase64,
    //         uploadedDocuments,
    //         dischargeSummary // Pass the discharge summary data with structured JSON
    //       );

    //       // Call the external bundle API
    //       console.log(`🚀 CALLING EXTERNAL API for DischargeSummary...`);
    //       const bundleApiResponse = await callExternalBundleApi(bundlePayload, 'DischargeSummary');
    //       console.log(`📡 EXTERNAL API RESPONSE RECEIVED:`, {
    //         hasResponse: !!bundleApiResponse,
    //         responseType: typeof bundleApiResponse,
    //         responseKeys: bundleApiResponse ? Object.keys(bundleApiResponse) : [],
    //       });

    //       // Store the returned FHIR bundle in the database
    //       if (bundleApiResponse) {
    //         console.log(`🔍 PROCESSING BUNDLE RESPONSE...`);
    //         const fhirBundle = bundleApiResponse.fhirBundle || bundleApiResponse;
    //         console.log(`📦 FHIR BUNDLE TO STORE:`, {
    //           bundleSize: JSON.stringify(fhirBundle).length,
    //           bundleKeys: Object.keys(fhirBundle || {}),
    //         });

    //         console.log(`💾 CALLING storeFhirBundleInDatabase...`);
    //         const storedBundle = await storeFhirBundleInDatabase(
    //           fhirBundle,
    //           validatedData.consultationId,
    //           dischargeSummary.patientId,
    //           organizationId,
    //           "DischargeSummary"
    //         );

    //         console.log(
    //           "✅ Successfully called external discharge summary bundle API and stored FHIR bundle",
    //           {
    //             consultationId: validatedData.consultationId,
    //             dischargeSummaryId: dischargeSummary.id,
    //             bundleId: storedBundle.bundleId,
    //             bundleSize: JSON.stringify(fhirBundle).length,
    //           },
    //         );
    //       }
    //     }
    //   }
    // } catch (bundleError) {
    //   console.error(
    //     "❌ Failed to call external discharge summary bundle API",
    //     {
    //       consultationId: validatedData.consultationId,
    //       dischargeSummaryId: dischargeSummary.id,
    //       error:
    //         bundleError instanceof Error
    //           ? bundleError.message
    //           : String(bundleError),
    //     },
    //   );
    //   // Don't fail the discharge summary creation if bundle generation fails
    // }

    return NextResponse.json(dischargeSummary, { status: 201 });
  } catch (error) {
    console.error("Error creating discharge summary:", error);
    return NextResponse.json(
      { error: "Failed to create discharge summary" },
      { status: 500 },
    );
  }
}
