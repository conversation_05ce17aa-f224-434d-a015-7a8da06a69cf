import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getUserFromRequestCookies } from "@/lib/auth-cookies";
import { db } from "@/lib/db";

// BACKUP NOTE: This file was modified to match upload-discharge-summary route structure
// Original version can be restored from git history if needed

// Schema for creating a discharge summary
const createDischargeSummarySchema = z.object({
  patientId: z.string(),
  doctorId: z.string(),
  consultationId: z.string().min(1, "Consultation ID is required"),
  admissionDate: z.string().optional(),
  dischargeDate: z.string().optional(),
  organizationId: z.string().optional(),

  // Chief Complaints
  chiefComplaint: z.string().optional(),

  // Medical History
  medicalHistory: z.string().optional(),

  // Investigations
  investigations: z.string().optional(),
  vitalSigns: z.string().optional(),
  labResults: z.string().optional(),

  // Procedures
  procedureName: z.string().optional(),
  procedureCode: z.string().optional(),
  procedureDate: z.string().optional(),
  proceduresPerformed: z.string().optional(),

  // Medications
  medicationName: z.string().optional(),
  medicationCode: z.string().optional(),
  medicationInstructions: z.string().optional(),
  medicationsList: z.string().optional(),

  // Care Plan
  carePlan: z.string().optional(),
  followUpDate: z.string().optional(),
  dischargeInstructions: z.string().optional(),
  recommendations: z.string().optional(),

  // Legacy fields
  diagnosisName: z.string().optional(),
  diagnosisCode: z.string().optional(),
  dischargeSummary: z.string().optional(),
  condition: z.string().optional(),
  treatmentProvided: z.string().optional(),
});

// GET /api/discharge-summary
export async function GET(req: NextRequest) {
  try {
    const user = getUserFromRequestCookies(req);

    const { searchParams } = new URL(req.url);
    const patientId = searchParams?.get("patientId");
    const consultationId = searchParams?.get("consultationId");

    let dischargeSummaries;
    if (patientId) {
      dischargeSummaries = await db.documentReference.findMany({
        where: {
          patientId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } else if (consultationId) {
      dischargeSummaries = await db.documentReference.findMany({
        where: {
          consultationId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    } else {
      // Get all discharge summaries for the user's organization
      if (!user.organizationId) {
        return NextResponse.json(
          { error: "User has no organization" },
          { status: 400 },
        );
      }

      dischargeSummaries = await db.documentReference.findMany({
        where: {
          organizationId: user.organizationId,
          type: "discharge-summary",
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          doctor: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          consultation: {
            include: {
              patient: {
                select: {
                  firstName: true,
                  lastName: true,
                  gender: true,
                  dateOfBirth: true,
                },
              },
              doctor: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    }
    // console.log("Discharge summaries:", dischargeSummaries.length);

    return NextResponse.json(dischargeSummaries);
  } catch (error) {
    console.error("Error fetching discharge summaries:", error);
    return NextResponse.json(
      { error: "Failed to fetch discharge summaries" },
      { status: 500 },
    );
  }
}

// POST /api/discharge-summary - Updated with better error handling
export async function POST(req: NextRequest) {
  console.log("=== DISCHARGE SUMMARY API STARTING ===");

  try {
    // Get user info from cookies (same pattern as other working APIs)
    const userInfoCookie = req.cookies.get("user-info");
    const sessionToken = req.cookies.get("session-token");

    console.log("User-info cookie:", userInfoCookie);
    console.log("Session-token cookie:", sessionToken);

    if (!userInfoCookie || !sessionToken) {
      console.log("No user info or session token found");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse the user info
    let userInfo;
    try {
      userInfo = JSON.parse(decodeURIComponent(userInfoCookie.value));
      console.log("Parsed user info:", userInfo);
    } catch (error) {
      console.log("Error parsing user info:", error);
      return NextResponse.json({ error: "Invalid session" }, { status: 401 });
    }

    const email = userInfo.email;
    const organizationId = userInfo.organizationId;

    if (!organizationId || !email) {
      console.log("No organization ID or email found in user info");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("User authenticated:", { email, organizationId });

    const json = await req.json();
    console.log(
      "Received discharge summary data:",
      JSON.stringify(json, null, 2),
    );

    try {
      const validatedData = createDischargeSummarySchema.parse(json);
      console.log(
        "Validated discharge summary data:",
        JSON.stringify(validatedData, null, 2),
      );
    } catch (validationError) {
      console.error("Validation error:", validationError);
      return NextResponse.json(
        { error: "Validation failed", details: validationError },
        { status: 400 },
      );
    }

    const validatedData = createDischargeSummarySchema.parse(json);

    // Organization ID is already validated above
    console.log("User organization ID:", organizationId);

    // Validate that the consultation exists and belongs to the correct patient and doctor
    const consultation = await db.consultation.findFirst({
      where: {
        id: validatedData.consultationId,
        patientId: validatedData.patientId,
        doctorId: validatedData.doctorId,
        organizationId: organizationId,
      },
    });

    if (!consultation) {
      return NextResponse.json(
        {
          error:
            "Invalid consultation: Consultation not found or does not match the selected patient and doctor",
        },
        { status: 400 },
      );
    }

    // Check if a discharge summary already exists for this consultation
    const existingDischargeSummary = await db.documentReference.findFirst({
      where: {
        consultationId: validatedData.consultationId,
        type: "discharge-summary",
      },
    });

    if (existingDischargeSummary) {
      return NextResponse.json(
        {
          error: "A discharge summary already exists for this consultation",
        },
        { status: 400 },
      );
    }

    // Create the discharge summary as a DocumentReference
    const dischargeSummary = await db.documentReference.create({
      data: {
        patientId: validatedData.patientId,
        doctorId: validatedData.doctorId,
        organizationId: organizationId,
        consultationId: validatedData.consultationId,
        status: "current",
        docStatus: "final",
        type: "discharge-summary",
        typeDisplay: "Discharge Summary",
        category: "clinical",
        categoryDisplay: "Clinical Document",
        subject: `Discharge Summary for Patient`,
        date: validatedData.dischargeDate
          ? new Date(validatedData.dischargeDate)
          : new Date(),
        author: userInfo.name || email || "Unknown",
        description: "Discharge Summary Document",
        content: {
          admissionDate: validatedData.admissionDate,
          dischargeDate: validatedData.dischargeDate,

          // Chief Complaints
          chiefComplaint: validatedData.chiefComplaint,

          // Medical History
          medicalHistory: validatedData.medicalHistory,

          // Investigations
          investigations: validatedData.investigations,
          vitalSigns: validatedData.vitalSigns,
          labResults: validatedData.labResults,

          // Procedures
          procedureName: validatedData.procedureName,
          procedureCode: validatedData.procedureCode,
          procedureDate: validatedData.procedureDate,
          proceduresPerformed: validatedData.proceduresPerformed,

          // Medications
          medicationName: validatedData.medicationName,
          medicationCode: validatedData.medicationCode,
          medicationInstructions: validatedData.medicationInstructions,
          medicationsList: validatedData.medicationsList,

          // Care Plan
          carePlan: validatedData.carePlan,
          followUpDate: validatedData.followUpDate,
          dischargeInstructions: validatedData.dischargeInstructions,
          recommendations: validatedData.recommendations,

          // Legacy fields
          diagnosisName: validatedData.diagnosisName,
          diagnosisCode: validatedData.diagnosisCode,
          dischargeSummary: validatedData.dischargeSummary,
          condition: validatedData.condition,
          treatmentProvided: validatedData.treatmentProvided,
        },
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            gender: true,
            dateOfBirth: true,
          },
        },
        doctor: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        consultation: {
          include: {
            patient: {
              select: {
                firstName: true,
                lastName: true,
                gender: true,
                dateOfBirth: true,
              },
            },
            doctor: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Automatically generate and store FHIR bundles for discharge summary using correct system
    try {
      const { storeFhirBundles } = await import("@/lib/fhir-bundle-storage");

      // Fetch organization data for proper bundle generation
      const organization = await db.organization.findFirst({
        where: {
          id: organizationId,
        },
      });

      // Generate discharge summary bundle using correct ABDM-compliant system
      const dischargeSummaryParams = {
        // Patient details
        patientID: dischargeSummary.patientId,
        patientFirstName: dischargeSummary.patient?.firstName || "N/A",
        patientLastName: dischargeSummary.patient?.lastName || "N/A",
        patientGender: dischargeSummary.patient?.gender || "N/A",
        patientBirthDate:
          dischargeSummary.patient?.dateOfBirth?.toISOString().split("T")[0] ||
          "N/A",

        // Practitioner details
        practitionerID: dischargeSummary.doctorId,
        practitionerName: dischargeSummary.doctor?.user?.name || "N/A",

        // Organization details
        organizationID: organizationId,
        organizationName: organization?.name || "N/A",

        consultationId: dischargeSummary.consultationId,

        // Discharge summary specific data
        compositionIdentifier: `discharge-summary-${dischargeSummary.id}`,
        compositionDate: new Date().toISOString(),
        compositionTitle: "Discharge Summary",

        // Content from the discharge summary
        chiefComplaint: (dischargeSummary.content as any)?.chiefComplaint || "",
        medicalHistory: (dischargeSummary.content as any)?.medicalHistory || "",
        investigations: (dischargeSummary.content as any)?.investigations || "",
        proceduresPerformed:
          (dischargeSummary.content as any)?.proceduresPerformed || "",
        medicationsList:
          (dischargeSummary.content as any)?.medicationsList || "",
        carePlan: (dischargeSummary.content as any)?.carePlan || "",
        dischargeInstructions:
          (dischargeSummary.content as any)?.dischargeInstructions || "",

        // Condition fields required by EntryConditionStrategy
        conditionCode:
          (dischargeSummary.content as any)?.diagnosisCode || "Z51.11",
        conditionDisplay:
          (dischargeSummary.content as any)?.diagnosisName ||
          "Encounter for antineoplastic chemotherapy",
        conditionText:
          (dischargeSummary.content as any)?.diagnosisName ||
          "Primary diagnosis",
        patientReference: `urn:uuid:patient-${dischargeSummary.patientId}`,
        practitionerReference: `urn:uuid:practitioner-${dischargeSummary.doctorId}`,
        recordedDate: new Date().toISOString(),
        onsetDate:
          (dischargeSummary.content as any)?.admissionDate ||
          new Date().toISOString(),
        clinicalStatus: "active",
        clinicalStatusDisplay: "Active",
        verificationStatus: "confirmed",
        verificationStatusDisplay: "Confirmed",
        category: "encounter-diagnosis",
        categoryDisplay: "Encounter Diagnosis",
        severityCode: "24484000",
        severity: "Severe",
        conditionRecordedDate: new Date().toISOString(),
        conditionOnsetDate:
          (dischargeSummary.content as any)?.admissionDate ||
          new Date().toISOString(),
        conditionClinicalStatus: "active",
        conditionClinicalStatusDisplay: "Active",
        conditionVerificationStatus: "confirmed",
        conditionVerificationStatusDisplay: "Confirmed",
        conditionCategory: "encounter-diagnosis",
        conditionCategoryDisplay: "Encounter Diagnosis",
      };

      // Use custom discharge summary strategy to include uploaded documents
      const { generateCustomDischargeSummaryBundle } = await import(
        "@/app/api/fhir/custom-discharge-summary-strategy"
      );
      const dischargeSummaryBundle = await generateCustomDischargeSummaryBundle(
        dischargeSummaryParams,
      );

      // Store the discharge summary bundle
      const bundlesToStore = {
        dischargeSummary: dischargeSummaryBundle,
      };

      const storedBundles = await storeFhirBundles(
        bundlesToStore,
        validatedData.consultationId,
        dischargeSummary.patientId,
        organizationId,
      );

      console.log("✅ Auto-generated FHIR discharge summary bundle", {
        consultationId: validatedData.consultationId,
        dischargeSummaryId: dischargeSummary.id,
        bundleCount: storedBundles.length,
        bundleTypes: storedBundles.map((b) => b.bundleType),
      });
    } catch (bundleError) {
      console.error(
        "❌ Failed to auto-generate FHIR bundles for discharge summary",
        {
          consultationId: validatedData.consultationId,
          dischargeSummaryId: dischargeSummary.id,
          error:
            bundleError instanceof Error
              ? bundleError.message
              : String(bundleError),
        },
      );
      // Don't fail the discharge summary creation if bundle generation fails
    }

    return NextResponse.json(dischargeSummary, { status: 201 });
  } catch (error) {
    console.error("Error creating discharge summary:", error);
    return NextResponse.json(
      { error: "Failed to create discharge summary" },
      { status: 500 },
    );
  }
}
