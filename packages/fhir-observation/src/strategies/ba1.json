{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "meta": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "security": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}, "versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["profile", "security", "versionId", "lastUpdated"]}, "type": {"type": "string"}, "entry": {"type": "array", "items": {"type": "object", "properties": {"fullUrl": {"type": "string"}, "resource": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "meta": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["profile"]}, "text": {"type": "object", "properties": {"div": {"type": "string"}, "status": {"type": "string"}}, "required": ["div", "status"]}, "resourceType": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}, "text": {"type": "string"}}, "required": ["coding"]}, "title": {"type": "string"}, "author": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}]}, "status": {"type": "string"}, "section": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "entry": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "title": {"type": "string"}}, "required": ["code", "entry", "title"]}}, "subject": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "language": {"type": "string"}, "custodian": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "encounter": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "confidentiality": {"type": "string"}, "name": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, {"type": "string"}, {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}]}, "identifier": {"type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}, "required": ["value", "system"]}}, "telecom": {"type": "array", "items": {"type": "object", "properties": {"use": {"type": "string"}, "value": {"type": "string"}, "system": {"type": "string"}}, "required": ["use", "value", "system"]}}, "gender": {"type": "string"}, "birthDate": {"type": "string", "format": "date"}, "class": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}, "period": {"type": "object", "properties": {"end": {"type": "string", "format": "date-time"}, "start": {"type": "string", "format": "date-time"}}, "required": ["end", "start"]}, "hospitalization": {"type": "object", "properties": {"dischargeDisposition": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, "required": ["dischargeDisposition"]}, "code": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "clinicalStatus": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "collection": {"type": "object", "properties": {"collectedDateTime": {"type": "string", "format": "date-time"}}, "required": ["collectedDateTime"]}, "receivedTime": {"type": "string", "format": "date-time"}, "issued": {"type": "string", "format": "date"}, "category": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}, "text": {"type": "string"}}, "required": ["coding"]}}, "specimen": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "performer": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "conclusion": {"type": "string"}, "resultsInterpreter": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "outcome": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}, "reasonCode": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, "performedDateTime": {"type": "string", "format": "date"}, "intent": {"type": "string"}, "requester": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "authoredOn": {"type": "string", "format": "date-time"}, "dosageInstruction": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "route": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "method": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}, "additionalInstruction": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, "required": ["text", "route", "method", "additionalInstruction"]}}, "medicationCodeableConcept": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "goal": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, "required": ["description"]}}, "note": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, "created": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "content": {"type": "array", "items": {"type": "object", "properties": {"attachment": {"type": "object", "properties": {"data": {"type": "string"}, "size": {"type": "integer"}, "title": {"type": "string"}, "contentType": {"type": "string"}}, "required": ["data", "size", "title", "contentType"]}}, "required": ["attachment"]}}, "context": {"type": "object", "properties": {"encounter": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}}, "required": ["encounter"]}, "docStatus": {"type": "string"}, "end": {"type": "string", "format": "date-time"}, "start": {"type": "string", "format": "date-time"}, "participant": {"type": "array", "items": {"type": "object", "properties": {"actor": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "status": {"type": "string"}}, "required": ["actor", "status"]}}, "serviceType": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}, "appointmentType": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "serviceCategory": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}}, "required": ["id", "meta", "text", "resourceType"]}}, "required": ["fullUrl", "resource"]}}, "signature": {"type": "object", "properties": {"who": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "data": {"type": "string"}, "type": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}, "when": {"type": "string", "format": "date-time"}, "sigFormat": {"type": "string"}}, "required": ["who", "data", "type", "when", "sigFormat"]}, "timestamp": {"type": "string", "format": "date-time"}, "identifier": {"type": "object", "properties": {"value": {"type": "string", "format": "uuid"}, "system": {"type": "string", "format": "uri"}}, "required": ["value", "system"]}, "resourceType": {"type": "string"}}, "required": ["id", "meta", "type", "entry", "signature", "timestamp", "identifier", "resourceType"]}