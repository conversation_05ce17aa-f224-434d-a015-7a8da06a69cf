{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"resourceType": {"type": "string"}, "id": {"type": "string"}, "meta": {"type": "object", "properties": {"versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}, "profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "security": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["versionId", "lastUpdated", "profile", "security"]}, "identifier": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "value": {"type": "string", "format": "uuid"}}, "required": ["system", "value"]}, "type": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "entry": {"type": "array", "items": {"type": "object", "properties": {"fullUrl": {"type": "string"}, "resource": {"type": "object", "properties": {"resourceType": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "meta": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["profile"]}, "text": {"type": "object", "properties": {"status": {"type": "string"}, "div": {"type": "string"}}, "required": ["status", "div"]}, "language": {"type": "string"}, "status": {"type": "string"}, "type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}, "text": {"type": "string"}}, "required": ["coding"]}, "subject": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "encounter": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "date": {"type": "string", "format": "date-time"}, "author": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "title": {"type": "string"}, "confidentiality": {"type": "string"}, "custodian": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "section": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "code": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}, "entry": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}}, "required": ["title", "code", "entry"]}}, "identifier": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "value": {"type": "string"}, "type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "required": ["system", "value"]}}, "name": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, {"type": "string"}, {"type": "string"}, {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}]}, "telecom": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string"}, "value": {"type": "string"}, "use": {"type": "string"}}, "required": ["system", "value", "use"]}}, "gender": {"type": "string"}, "birthDate": {"type": "string", "format": "date"}, "class": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}, "period": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}}, "required": ["start", "end"]}, "hospitalization": {"type": "object", "properties": {"dischargeDisposition": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}, "text": {"type": "string"}}, "required": ["coding", "text"]}}, "required": ["dischargeDisposition"]}, "serviceCategory": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "serviceType": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string", "format": "date"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "appointmentType": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}, "reasonReference": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "description": {"type": "string"}, "start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "created": {"type": "string", "format": "date-time"}, "participant": {"type": "array", "items": {"type": "object", "properties": {"actor": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "status": {"type": "string"}}, "required": ["actor", "status"]}}, "clinicalStatus": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}, "code": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}, "text": {"type": "string"}}, "required": ["coding", "text"]}, "receivedTime": {"type": "string", "format": "date-time"}, "collection": {"type": "object", "properties": {"collectedDateTime": {"type": "string", "format": "date-time"}}, "required": ["collectedDateTime"]}, "category": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "issued": {"type": "string", "format": "date-time"}, "performer": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "resultsInterpreter": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "specimen": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "result": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}, "conclusion": {"type": "string"}, "conclusionCode": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "presentedForm": {"type": "array", "items": {"type": "object", "properties": {"contentType": {"type": "string"}, "language": {"type": "string"}, "data": {"type": "string"}, "title": {"type": "string"}}, "required": ["contentType", "language", "data", "title"]}}, "effectiveDateTime": {"type": "string", "format": "date"}, "valueQuantity": {"type": "object", "properties": {"value": {"type": "integer"}, "unit": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "code": {"type": "string"}}, "required": ["value", "unit", "system", "code"]}, "referenceRange": {"type": "array", "items": {"type": "object", "properties": {"high": {"type": "object", "properties": {"value": {"type": "integer"}, "unit": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "code": {"type": "string"}}, "required": ["value", "unit", "system", "code"]}, "low": {"type": "object", "properties": {"value": {"type": "integer"}, "unit": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "code": {"type": "string"}}, "required": ["value", "unit", "system", "code"]}}}}, "performedDateTime": {"type": "string", "format": "date"}, "complication": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "intent": {"type": "string"}, "medicationCodeableConcept": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}, "authoredOn": {"type": "string", "format": "date"}, "requester": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "dosageInstruction": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "additionalInstruction": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "timing": {"type": "object", "properties": {"repeat": {"type": "object", "properties": {"frequency": {"type": "integer"}, "period": {"type": "integer"}, "periodUnit": {"type": "string"}}, "required": ["frequency", "period", "periodUnit"]}}, "required": ["repeat"]}, "route": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string", "format": "date"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}, "method": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string", "format": "uri"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}}, "required": ["coding"]}}, "required": ["text", "additionalInstruction", "timing", "route", "method"]}}, "activity": {"type": "array", "items": {"type": "object", "properties": {"outcomeReference": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}}}, "required": ["outcomeReference"]}}, "docStatus": {"type": "string"}, "content": {"type": "array", "items": {"type": "object", "properties": {"attachment": {"type": "object", "properties": {"contentType": {"type": "string"}, "language": {"type": "string"}, "data": {"type": "string"}, "title": {"type": "string"}, "creation": {"type": "string", "format": "date-time"}}, "required": ["contentType", "language", "data", "title", "creation"]}}, "required": ["attachment"]}}}, "required": ["resourceType", "id", "meta", "text"]}}, "required": ["fullUrl", "resource"]}}, "signature": {"type": "object", "properties": {"type": {"type": "array", "items": {"type": "object", "properties": {"system": {"type": "string"}, "code": {"type": "string"}, "display": {"type": "string"}}, "required": ["system", "code", "display"]}}, "when": {"type": "string", "format": "date-time"}, "who": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference", "display"]}, "sigFormat": {"type": "string"}, "data": {"type": "string"}}, "required": ["type", "when", "who", "sigFormat", "data"]}}, "required": ["resourceType", "id", "meta", "identifier", "type", "timestamp", "entry", "signature"]}